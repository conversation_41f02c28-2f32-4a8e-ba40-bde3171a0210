<?xml version="1.0" encoding="UTF-8"?>
<svg viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
  <!-- 标题 -->
  <text x="400" y="40" font-family="Arial" font-size="24" text-anchor="middle" font-weight="bold" fill="#333">市场风险估值完整流程</text>
  
  <!-- 1. 数据准备阶段 -->
  <rect x="50" y="80" width="700" height="100" rx="10" fill="#E3F2FD" stroke="#2196F3" stroke-width="2"/>
  <text x="400" y="105" font-family="Arial" font-size="16" text-anchor="middle" font-weight="bold" fill="#0D47A1">数据准备阶段</text>
  
  <!-- 数据准备内部流程 -->
  <rect x="80" y="120" width="150" height="40" rx="5" fill="#BBDEFB" stroke="#64B5F6" stroke-width="1"/>
  <text x="155" y="145" font-family="Arial" font-size="12" text-anchor="middle" fill="#333">头寸数据</text>
  
  <rect x="250" y="120" width="150" height="40" rx="5" fill="#BBDEFB" stroke="#64B5F6" stroke-width="1"/>
  <text x="325" y="145" font-family="Arial" font-size="12" text-anchor="middle" fill="#333">市场数据</text>
  
  <rect x="420" y="120" width="150" height="40" rx="5" fill="#BBDEFB" stroke="#64B5F6" stroke-width="1"/>
  <text x="495" y="145" font-family="Arial" font-size="12" text-anchor="middle" fill="#333">历史市场数据</text>
  
  <rect x="590" y="120" width="150" height="40" rx="5" fill="#BBDEFB" stroke="#64B5F6" stroke-width="1"/>
  <text x="665" y="145" font-family="Arial" font-size="12" text-anchor="middle" fill="#333">控制参数</text>
  
  <!-- 2. 估值引擎 -->
  <rect x="50" y="200" width="340" height="180" rx="10" fill="#E8F5E9" stroke="#4CAF50" stroke-width="2"/>
  <text x="220" y="225" font-family="Arial" font-size="16" text-anchor="middle" font-weight="bold" fill="#1B5E20">估值引擎</text>
  
  <!-- 估值引擎内部流程 -->
  <rect x="80" y="240" width="280" height="40" rx="5" fill="#C8E6C9" stroke="#81C784" stroke-width="1"/>
  <text x="220" y="265" font-family="Arial" font-size="12" text-anchor="middle" fill="#333">数据预处理与头寸拆分</text>
  
  <rect x="80" y="290" width="130" height="40" rx="5" fill="#C8E6C9" stroke="#81C784" stroke-width="1"/>
  <text x="145" y="315" font-family="Arial" font-size="12" text-anchor="middle" fill="#333">固定利率债券模型</text>
  
  <rect x="80" y="340" width="130" height="40" rx="5" fill="#C8E6C9" stroke="#81C784" stroke-width="1"/>
  <text x="145" y="365" font-family="Arial" font-size="12" text-anchor="middle" fill="#333">外汇远期模型</text>
  
  <rect x="230" y="290" width="130" height="40" rx="5" fill="#C8E6C9" stroke="#81C784" stroke-width="1"/>
  <text x="295" y="315" font-family="Arial" font-size="12" text-anchor="middle" fill="#333">含权债券BDT模型</text>
  
  <rect x="230" y="340" width="130" height="40" rx="5" fill="#C8E6C9" stroke="#81C784" stroke-width="1"/>
  <text x="295" y="365" font-family="Arial" font-size="12" text-anchor="middle" fill="#333">其他金融工具模型</text>
  
  <!-- 3. 情景生成 -->
  <rect x="410" y="200" width="340" height="180" rx="10" fill="#FFF3E0" stroke="#FF9800" stroke-width="2"/>
  <text x="580" y="225" font-family="Arial" font-size="16" text-anchor="middle" font-weight="bold" fill="#E65100">情景生成</text>
  
  <!-- 情景生成内部流程 -->
  <rect x="440" y="240" width="280" height="40" rx="5" fill="#FFE0B2" stroke="#FFB74D" stroke-width="1"/>
  <text x="580" y="265" font-family="Arial" font-size="12" text-anchor="middle" fill="#333">历史模拟法情景生成</text>
  
  <rect x="440" y="290" width="130" height="40" rx="5" fill="#FFE0B2" stroke="#FFB74D" stroke-width="1"/>
  <text x="505" y="315" font-family="Arial" font-size="12" text-anchor="middle" fill="#333">利率情景</text>
  
  <rect x="440" y="340" width="130" height="40" rx="5" fill="#FFE0B2" stroke="#FFB74D" stroke-width="1"/>
  <text x="505" y="365" font-family="Arial" font-size="12" text-anchor="middle" fill="#333">汇率情景</text>
  
  <rect x="590" y="290" width="130" height="40" rx="5" fill="#FFE0B2" stroke="#FFB74D" stroke-width="1"/>
  <text x="655" y="315" font-family="Arial" font-size="12" text-anchor="middle" fill="#333">股票情景</text>
  
  <rect x="590" y="340" width="130" height="40" rx="5" fill="#FFE0B2" stroke="#FFB74D" stroke-width="1"/>
  <text x="655" y="365" font-family="Arial" font-size="12" text-anchor="middle" fill="#333">商品情景</text>
  
  <!-- 4. 风险计量引擎 -->
  <rect x="50" y="400" width="700" height="150" rx="10" fill="#F3E5F5" stroke="#9C27B0" stroke-width="2"/>
  <text x="400" y="425" font-family="Arial" font-size="16" text-anchor="middle" font-weight="bold" fill="#4A148C">风险计量引擎</text>
  
  <!-- 风险计量引擎内部流程 -->
  <rect x="80" y="440" width="200" height="40" rx="5" fill="#E1BEE7" stroke="#CE93D8" stroke-width="1"/>
  <text x="180" y="465" font-family="Arial" font-size="12" text-anchor="middle" fill="#333">情景估值计算</text>
  
  <rect x="300" y="440" width="200" height="40" rx="5" fill="#E1BEE7" stroke="#CE93D8" stroke-width="1"/>
  <text x="400" y="465" font-family="Arial" font-size="12" text-anchor="middle" fill="#333">计算损益序列</text>
  
  <rect x="520" y="440" width="200" height="40" rx="5" fill="#E1BEE7" stroke="#CE93D8" stroke-width="1"/>
  <text x="620" y="465" font-family="Arial" font-size="12" text-anchor="middle" fill="#333">排序和分位数计算</text>
  
  <rect x="80" y="490" width="310" height="40" rx="5" fill="#E1BEE7" stroke="#CE93D8" stroke-width="1"/>
  <text x="235" y="515" font-family="Arial" font-size="12" text-anchor="middle" fill="#333">流动性期限划分调整</text>
  
  <rect x="410" y="490" width="310" height="40" rx="5" fill="#E1BEE7" stroke="#CE93D8" stroke-width="1"/>
  <text x="565" y="515" font-family="Arial" font-size="12" text-anchor="middle" fill="#333">计算ES风险指标</text>

  <!-- 5. 结果输出 -->
  <rect x="50" y="570" width="700" height="100" rx="10" fill="#FFEBEE" stroke="#F44336" stroke-width="2"/>
  <text x="400" y="595" font-family="Arial" font-size="16" text-anchor="middle" font-weight="bold" fill="#B71C1C">结果输出</text>
  
  <!-- 结果输出内部流程 -->
  <rect x="80" y="610" width="150" height="40" rx="5" fill="#FFCDD2" stroke="#EF9A9A" stroke-width="1"/>
  <text x="155" y="635" font-family="Arial" font-size="12" text-anchor="middle" fill="#333">估值报告</text>
  
  <rect x="250" y="610" width="150" height="40" rx="5" fill="#FFCDD2" stroke="#EF9A9A" stroke-width="1"/>
  <text x="325" y="635" font-family="Arial" font-size="12" text-anchor="middle" fill="#333">风险分解分析</text>
  
  <rect x="420" y="610" width="150" height="40" rx="5" fill="#FFCDD2" stroke="#EF9A9A" stroke-width="1"/>
  <text x="495" y="635" font-family="Arial" font-size="12" text-anchor="middle" fill="#333">风险报告</text>
  
  <rect x="590" y="610" width="150" height="40" rx="5" fill="#FFCDD2" stroke="#EF9A9A" stroke-width="1"/>
  <text x="665" y="635" font-family="Arial" font-size="12" text-anchor="middle" fill="#333">可视化展示</text>
  
  <!-- 连接箭头 -->
  <!-- 数据准备到估值引擎 -->
  <line x1="220" y1="180" x2="220" y2="200" stroke="#333" stroke-width="2" stroke-dasharray="5,3"/>
  <polygon points="220,200 215,190 225,190" fill="#333"/>
  
  <!-- 数据准备到情景生成 -->
  <line x1="580" y1="180" x2="580" y2="200" stroke="#333" stroke-width="2" stroke-dasharray="5,3"/>
  <polygon points="580,200 575,190 585,190" fill="#333"/>
  
  <!-- 估值引擎到风险计量引擎 -->
  <line x1="220" y1="380" x2="220" y2="400" stroke="#333" stroke-width="2" stroke-dasharray="5,3"/>
  <polygon points="220,400 215,390 225,390" fill="#333"/>
  
  <!-- 情景生成到风险计量引擎 -->
  <line x1="580" y1="380" x2="580" y2="400" stroke="#333" stroke-width="2" stroke-dasharray="5,3"/>
  <polygon points="580,400 575,390 585,390" fill="#333"/>
  
  <!-- 风险计量引擎到结果输出 -->
  <line x1="400" y1="550" x2="400" y2="570" stroke="#333" stroke-width="2" stroke-dasharray="5,3"/>
  <polygon points="400,570 395,560 405,560" fill="#333"/>
</svg>