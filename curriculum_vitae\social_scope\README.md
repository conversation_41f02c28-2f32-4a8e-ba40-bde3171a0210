# 简历编译说明

## 问题原因

在本地环境中编译 `cv-llt.tex` 时，"论文发表"部分没有内容的原因是：

1. **缺少 Biber 处理步骤**：biblatex 需要 biber 来处理参考文献数据库
2. **编译顺序不正确**：需要按照特定顺序运行 XeLaTeX → Biber → XeLaTeX → XeLaTeX

而在 Overleaf 中可以正常显示是因为 Overleaf 会自动处理这个编译流程。

## 解决方案

### 方法一：使用提供的编译脚本

#### Windows 用户：
```bash
./compile.bat
```

#### Linux/Mac 用户：
```bash
chmod +x compile.sh
./compile.sh
```

### 方法二：手动编译

按照以下顺序运行命令：

```bash
# 第一步：运行 XeLaTeX
xelatex cv-llt.tex

# 第二步：运行 Biber 处理参考文献
biber cv-llt

# 第三步：再次运行 XeLaTeX
xelatex cv-llt.tex

# 第四步：最后一次运行 XeLaTeX（确保所有引用正确）
xelatex cv-llt.tex
```

## 文件说明

- `cv-llt.tex` - 主文件
- `cite-self.bib` - 参考文献数据库
- `publications.tex` - 论文发表部分的模板
- `settings.sty` - 样式设置文件
- `compile.bat` / `compile.sh` - 自动编译脚本

## 注意事项

1. 确保系统已安装 XeLaTeX 和 Biber
2. 如果修改了 `.bib` 文件，需要重新运行完整的编译流程
3. 编译过程中可能会有一些警告，这是正常的，只要最终生成了 PDF 文件即可

## 编译成功标志

编译成功后会看到：
- 生成 `cv-llt.pdf` 文件（2页）
- "论文发表"部分显示期刊文章列表
- 您的姓名在参考文献中会以粗体显示
