\begin{rubric}{项目经历}
  \subrubric{智能信贷风控模型与多源数据融合分析}
  \textbf{项目背景：}该项目旨在通过机器学习技术，精准预测客户贷款违约风险。项目核心挑战在于如何利用多维度的匿名异构数据，构建高精度的风险预测模型。

  \textbf{职责与贡献：}

  • 整合多信源的数据表（包括客户申请信息、征信局历史数据、历史贷款记录、信用卡月度账单和匿名特征等），处理超过数百万条记录和数百个原始特征的复杂数据集。

  • 设计并实施了多种缺失值填充策略、异常值检测与处理，以及数据类型优化，将数据处理阶段的内存占用降低了 60\%。

  • 将多个来源的非结构化和半结构化数据进行连接与对齐，构建了一个统一的、可供模型使用的宽表视图。

  • 总计构建了超过500个具有强预测能力的衍生特征，使模型的 AUC (Area Under ROC Curve) 相比基线模型提升了 15\%。

  • 构建了以 LightGBM 为核心的梯度提升决策树模型，并通过贝叶斯优化进行超参数调优，实现了模型性能的最优化。

  • 实施了模型集成策略（Ensemble Learning），融合了多个模型的预测结果，进一步提升了模型的稳定性和泛化能力，最终取得了优异的预测效果。
  \textbf{项目成果：}构建了一个可配置、可扩展且易于维护的机器学习流水线，在海量测试样本上表现出色，AUC分数达到0.8+。

  \subrubric{金融时序数据的高级混合建模与算法优化}
  \textbf{项目背景：}针对金融市场的高频交易数据，开发先进的时序预测模型，提升交易策略的准确性和盈利能力。

  \textbf{技术路线：}

  1. \textbf{数据预处理阶段}：清洗高频交易数据，处理缺失值和异常值，构建标准化的时序特征工程流水线。

  2. \textbf{模型构建阶段}：结合LSTM、GRU和Transformer架构，设计混合神经网络模型捕捉长短期依赖关系。

  3. \textbf{优化调参阶段}：采用网格搜索和贝叶斯优化相结合的策略，实现模型超参数的自动化调优。

  4. \textbf{验证部署阶段}：通过回测验证模型性能，部署到生产环境进行实时预测。
  %
  \textbf{项目成果：}模型在多个金融产品上的预测准确率提升了25\%，为公司带来了显著的投资收益。

\end{rubric}