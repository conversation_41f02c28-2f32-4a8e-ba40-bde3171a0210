\begin{frame}{NISQ algorithm: QAOA}		
    \small
    \textit{量子近似优化算法（Quantum Approximate Optimization Algorithm）是一种基于量子电路的二次优化（Quadratic Optimization），
    用于求解金融中的投资组合优化问题。基于马科维茨均值-方差模型（Markowitz Mean-Variance Analysis）。}
    \vspace{8pt} % 增加间距
    % \begin{table}[htpb]
    %     \centering
    %     \caption{编号与含义}
    %     \label{tab:number}
    %     \begin{tabular}{cl}\toprule
    %         编号 & 含义 \\\midrule
    %         1 & 4.0\\
    %         2 & 3.7\\\bottomrule
    %     \end{tabular}
    % \end{table}
    \begin{columns}[T] % T 表示顶部对齐
        % 左半部分
        \begin{column}{0.48\textwidth}
            \raggedleft
              \textbf{经典优化的马科维兹模型}
              \vspace{0.5cm}
            \begin{align}
                \min \quad & w^T \Sigma w \\
                \text{s.t.} \quad & \mu^T w \geq r \\
                & \sum w = 1
            \end{align}
        \end{column}
        
        % 右半部分
        \begin{column}{0.48\textwidth}
            \raggedleft
            \textbf{量子优化的马科维兹模型}
            \vspace{0.5cm}
            \begin{align}
                \min \quad & \frac{1}{2} q w^T \Sigma w - \mu^T w \\
                \text{s.t.} \quad & \sum w = 1
            \end{align}
        \end{column}
    \end{columns}
\end{frame}