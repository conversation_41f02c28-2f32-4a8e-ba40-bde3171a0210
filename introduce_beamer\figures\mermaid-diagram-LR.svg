<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 1100 203.5" style="max-width: 1100px;" class="flowchart" xmlns="http://www.w3.org/2000/svg" width="100%" id="export-svg"><style xmlns="http://www.w3.org/1999/xhtml">@import url("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css"); p {margin: 0;}</style><style>#export-svg{font-family:arial,sans-serif;font-size:14px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#export-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#export-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#export-svg .error-icon{fill:#ffffff;}#export-svg .error-text{fill:#000000;stroke:#000000;}#export-svg .edge-thickness-normal{stroke-width:2px;}#export-svg .edge-thickness-thick{stroke-width:3.5px;}#export-svg .edge-pattern-solid{stroke-dasharray:0;}#export-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#export-svg .edge-pattern-dashed{stroke-dasharray:3;}#export-svg .edge-pattern-dotted{stroke-dasharray:2;}#export-svg .marker{fill:#000000;stroke:#000000;}#export-svg .marker.cross{stroke:#000000;}#export-svg svg{font-family:arial,sans-serif;font-size:14px;}#export-svg p{margin:0;}#export-svg .label{font-family:arial,sans-serif;color:#333;}#export-svg .cluster-label text{fill:#000000;}#export-svg .cluster-label span{color:#000000;}#export-svg .cluster-label span p{background-color:transparent;}#export-svg .label text,#export-svg span{fill:#333;color:#333;}#export-svg .node rect,#export-svg .node circle,#export-svg .node ellipse,#export-svg .node polygon,#export-svg .node path{fill:#ffffff;stroke:#000000;stroke-width:2px;}#export-svg .rough-node .label text,#export-svg .node .label text,#export-svg .image-shape .label,#export-svg .icon-shape .label{text-anchor:middle;}#export-svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#export-svg .rough-node .label,#export-svg .node .label,#export-svg .image-shape .label,#export-svg .icon-shape .label{text-align:center;}#export-svg .node.clickable{cursor:pointer;}#export-svg .root .anchor path{fill:#000000!important;stroke-width:0;stroke:#000000;}#export-svg .arrowheadPath{fill:#000000;}#export-svg .edgePath .path{stroke:#000000;stroke-width:2px;}#export-svg .flowchart-link{stroke:#000000;fill:none;}#export-svg .edgeLabel{background-color:hsl(-120, 0%, 80%);text-align:center;}#export-svg .edgeLabel p{background-color:hsl(-120, 0%, 80%);}#export-svg .edgeLabel rect{opacity:0.5;background-color:hsl(-120, 0%, 80%);fill:hsl(-120, 0%, 80%);}#export-svg .labelBkg{background-color:rgba(204, 204, 204, 0.5);}#export-svg .cluster rect{fill:#ffffff;stroke:hsl(0, 0%, 90%);stroke-width:2px;}#export-svg .cluster text{fill:#000000;}#export-svg .cluster span{color:#000000;}#export-svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:arial,sans-serif;font-size:12px;background:#ffffff;border:1px solid hsl(0, 0%, 90%);border-radius:2px;pointer-events:none;z-index:100;}#export-svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#export-svg rect.text{fill:none;stroke-width:0;}#export-svg .icon-shape,#export-svg .image-shape{background-color:hsl(-120, 0%, 80%);text-align:center;}#export-svg .icon-shape p,#export-svg .image-shape p{background-color:hsl(-120, 0%, 80%);padding:2px;}#export-svg .icon-shape rect,#export-svg .image-shape rect{opacity:0.5;background-color:hsl(-120, 0%, 80%);fill:hsl(-120, 0%, 80%);}#export-svg .node .neo-node{stroke:#000000;}#export-svg [data-look="neo"].node rect,#export-svg [data-look="neo"].cluster rect,#export-svg [data-look="neo"].node polygon{stroke:url(#export-svg-gradient);filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#export-svg [data-look="neo"].node path{stroke:url(#export-svg-gradient);}#export-svg [data-look="neo"].node .outer-path{filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#export-svg [data-look="neo"].node .neo-line path{stroke:hsl(0, 0%, 70%);filter:none;}#export-svg [data-look="neo"].node circle{stroke:url(#export-svg-gradient);filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#export-svg [data-look="neo"].node circle .state-start{fill:#000000;}#export-svg [data-look="neo"].statediagram-cluster rect{fill:#ffffff;stroke:url(#export-svg-gradient);stroke-width:1px;}#export-svg [data-look="neo"].icon-shape .icon{fill:url(#export-svg-gradient);filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#export-svg [data-look="neo"].icon-shape .icon-neo path{stroke:url(#export-svg-gradient);filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#export-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="14" markerWidth="10.5" markerUnits="userSpaceOnUse" refY="7" refX="7.75" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="export-svg_flowchart-v2-pointEnd"><path style="stroke-width: 0px; stroke-dasharray: 1px, 0px;" class="arrowMarkerPath" d="M 0 0 L 11.5 7 L 0 14 z"/></marker><marker orient="auto" markerHeight="14" markerWidth="11.5" markerUnits="userSpaceOnUse" refY="7" refX="4" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="export-svg_flowchart-v2-pointStart"><polygon style="stroke-width: 0px; stroke-dasharray: 1px, 0px;" class="arrowMarkerPath" points="0,7 11.5,14 11.5,0"/></marker><marker orient="auto" markerHeight="14" markerWidth="10.5" markerUnits="userSpaceOnUse" refY="7" refX="11.5" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="export-svg_flowchart-v2-pointEnd-margin"><path style="stroke-width: 0px; stroke-dasharray: 1px, 0px;" class="arrowMarkerPath" d="M 0 0 L 11.5 7 L 0 14 z"/></marker><marker orient="auto" markerHeight="14" markerWidth="11.5" markerUnits="userSpaceOnUse" refY="7" refX="1" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="export-svg_flowchart-v2-pointStart-margin"><polygon style="stroke-width: 0px; stroke-dasharray: 1px, 0px;" class="arrowMarkerPath" points="0,7 11.5,14 11.5,0"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refX="10.75" refY="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="export-svg_flowchart-v2-circleEnd"><circle style="stroke-width: 0px; stroke-dasharray: 1px, 0px;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refY="5" refX="0" viewBox="0 0 10 10" class="marker flowchart-v2" id="export-svg_flowchart-v2-circleStart"><circle style="stroke-width: 0px; stroke-dasharray: 1px, 0px;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refX="12.25" refY="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="export-svg_flowchart-v2-circleEnd-margin"><circle style="stroke-width: 0px; stroke-dasharray: 1px, 0px;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refY="5" refX="-2" viewBox="0 0 10 10" class="marker flowchart-v2" id="export-svg_flowchart-v2-circleStart-margin"><circle style="stroke-width: 0px; stroke-dasharray: 1px, 0px;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="17.7" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="export-svg_flowchart-v2-crossEnd"><path style="stroke-width: 2.5px;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="-3.5" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="export-svg_flowchart-v2-crossStart"><path style="stroke-width: 2.5px; stroke-dasharray: 1px, 0px;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="17.7" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="export-svg_flowchart-v2-crossEnd-margin"><path style="stroke-width: 2.5px;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="-3.5" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="export-svg_flowchart-v2-crossStart-margin"><path style="stroke-width: 2.5px; stroke-dasharray: 1px, 0px;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><g class="root"><g class="clusters"/><g class="edgePaths"><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6OTYsInkiOjc4fSx7IngiOjEyMSwieSI6Nzh9LHsieCI6MTQ2LCJ5Ijo3OH1d" data-id="L_A_B_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 37 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M96,78L121,78L142,78"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MjkwLCJ5Ijo3OH0seyJ4IjozMTUsInkiOjc4fSx7IngiOjM0MCwieSI6Nzh9XQ==" data-id="L_B_C_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 37 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_0" d="M290,78L315,78L336,78"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NDU2LCJ5Ijo3OH0seyJ4Ijo0ODEsInkiOjc4fSx7IngiOjUwNiwieSI6Nzh9XQ==" data-id="L_C_D_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 37 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_D_0" d="M456,78L481,78L502,78"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NjIzLjk0NzM2ODQyMTA1MjYsInkiOjU1LjV9LHsieCI6Njc1LCJ5IjozMC41fSx7IngiOjcxNCwieSI6MzAuNX1d" data-id="L_D_E_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 82.24451446533203 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_E_0" d="M623.9473684210526,55.5L659.2832495461139,38.19634687174835Q675,30.5 692.5,30.5L710,30.5"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NjIzLjk0NzM2ODQyMTA1MjYsInkiOjEwMC41fSx7IngiOjY3NSwieSI6MTI1LjV9LHsieCI6NzAwLCJ5IjoxMjUuNX1d" data-id="L_D_F_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 68.4847412109375 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_F_0" d="M623.9473684210526,100.5L665.5699497276684,120.88219187695098Q675,125.5 685.5,125.5L696,125.5"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6Nzg3LCJ5IjoxMDN9LHsieCI6ODI3LCJ5Ijo3OH0seyJ4Ijo4NTksInkiOjc4fV0=" data-id="L_F_G_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 65.44941711425781 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_G_0" d="M787,103L815.1280237439288,85.41998516004452Q827,78 841,78L855,78"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6Nzg3LCJ5IjoxNDh9LHsieCI6ODI3LCJ5IjoxNzN9LHsieCI6ODUyLCJ5IjoxNzN9XQ==" data-id="L_F_H_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 58.62950897216797 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_H_0" d="M787,148L818.0960178079466,167.4350111299666Q827,173 837.5,173L848,173"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6OTQ3LCJ5Ijo3OH0seyJ4Ijo5NzksInkiOjc4fSx7IngiOjEwMTUuMzE1Nzg5NDczNjg0MiwieSI6MTAzfV0=" data-id="L_G_I_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 62.131378173828125 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_I_0" d="M947,78L963,78Q979,78 992.1790953106868,87.07256561242929L1012.0210156460125,100.73185859689268"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6OTU0LCJ5IjoxNzN9LHsieCI6OTc5LCJ5IjoxNzN9LHsieCI6MTAxNS4zMTU3ODk0NzM2ODQyLCJ5IjoxNDh9XQ==" data-id="L_H_I_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 55.340843200683594 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_I_0" d="M954,173L966.5,173Q979,173 989.2961682114741,165.91205811528962L1012.0210156460125,150.26814140310734"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_A_B_0" class="label"><foreignObject height="0" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_B_C_0" class="label"><foreignObject height="0" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_C_D_0" class="label"><foreignObject height="0" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_D_E_0" class="label"><foreignObject height="0" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_D_F_0" class="label"><foreignObject height="0" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_F_G_0" class="label"><foreignObject height="0" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_F_H_0" class="label"><foreignObject height="0" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_G_I_0" class="label"><foreignObject height="0" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_H_I_0" class="label"><foreignObject height="0" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(52, 78)" data-look="neo" data-et="node" data-node="true" data-id="A" id="flowchart-A-0" class="node default"><rect stroke="url(#gradient)" height="45" width="88" y="-22.5" x="-44" data-id="A" style="" class="basic label-container"/><g transform="translate(-28, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="56"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>债券比重</p></span></div></foreignObject></g></g><g transform="translate(218, 78)" data-look="neo" data-et="node" data-node="true" data-id="B" id="flowchart-B-1" class="node default"><rect stroke="url(#gradient)" height="45" width="144" y="-22.5" x="-72" data-id="B" style="" class="basic label-container"/><g transform="translate(-56, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="112"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>波动存款比总负债</p></span></div></foreignObject></g></g><g transform="translate(398, 78)" data-look="neo" data-et="node" data-node="true" data-id="C" id="flowchart-C-3" class="node default"><rect stroke="url(#gradient)" height="45" width="116" y="-22.5" x="-58" data-id="C" style="" class="basic label-container"/><g transform="translate(-42, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="84"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>短期投资比重</p></span></div></foreignObject></g></g><g transform="translate(578, 78)" data-look="neo" data-et="node" data-node="true" data-id="D" id="flowchart-D-5" class="node default"><rect stroke="url(#gradient)" height="45" width="144" y="-22.5" x="-72" data-id="D" style="" class="basic label-container"/><g transform="translate(-56, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="112"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>央行信贷比总负债</p></span></div></foreignObject></g></g><g transform="translate(751, 30.5)" data-look="neo" data-et="node" data-node="true" data-id="E" id="flowchart-E-7" class="node default"><rect stroke="url(#gradient)" height="45" width="74" y="-22.5" x="-37" data-id="E" style="" class="basic label-container"/><g transform="translate(-21, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="42"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>移动比</p></span></div></foreignObject></g></g><g transform="translate(751, 125.5)" data-look="neo" data-et="node" data-node="true" data-id="F" id="flowchart-F-9" class="node default"><rect stroke="url(#gradient)" height="45" width="102" y="-22.5" x="-51" data-id="F" style="" class="basic label-container"/><g transform="translate(-35, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="70"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>拆入拆出比</p></span></div></foreignObject></g></g><g transform="translate(903, 78)" data-look="neo" data-et="node" data-node="true" data-id="G" id="flowchart-G-11" class="node default"><rect stroke="url(#gradient)" height="45" width="88" y="-22.5" x="-44" data-id="G" style="" class="basic label-container"/><g transform="translate(-28, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="56"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>存贷款比</p></span></div></foreignObject></g></g><g transform="translate(903, 173)" data-look="neo" data-et="node" data-node="true" data-id="H" id="flowchart-H-13" class="node default"><rect stroke="url(#gradient)" height="45" width="102" y="-22.5" x="-51" data-id="H" style="" class="basic label-container"/><g transform="translate(-35, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="70"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>长期存款比</p></span></div></foreignObject></g></g><g transform="translate(1048, 125.5)" data-look="neo" data-et="node" data-node="true" data-id="I" id="flowchart-I-15" class="node default"><rect stroke="url(#gradient)" height="45" width="88" y="-22.5" x="-44" data-id="I" style="" class="basic label-container"/><g transform="translate(-28, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="56"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>流动比率</p></span></div></foreignObject></g></g></g></g></g><defs><filter width="130%" height="130%" id="drop-shadow"><feDropShadow flood-color="#FFFFFF" flood-opacity="0.06" stdDeviation="0" dy="4" dx="4"/></filter></defs><defs><filter width="150%" height="150%" id="drop-shadow-small"><feDropShadow flood-color="#FFFFFF" flood-opacity="0.06" stdDeviation="0" dy="2" dx="2"/></filter></defs><linearGradient y2="0%" x2="100%" y1="0%" x1="0%" gradientUnits="objectBoundingBox" id="export-svg-gradient"><stop stop-opacity="1" stop-color="#0042eb" offset="0%"/><stop stop-opacity="1" stop-color="#eb0042" offset="100%"/></linearGradient></svg>