%------------------------------------------------

\begin{frame}[fragile]
  \frametitle{用于情绪分析的 BERT 模型}

  \begin{block}{}
    \begin{itemize}
      \item \textbf{输入}：文本"公司第三季度盈利超预期"
      \item \textbf{Tokenization}：（[CLS], 公司, 第三季度, 盈利, 超, 预期, [SEP], ...）
      \item \textbf{BERT 架构}：基于 Transformer，12+ 层，768 维嵌入
      \item \textbf{微调情绪任务}：添加分类头（3类：正面、中性、负面）
      \item \textbf{输出}：[CLS]Tokens 的嵌入向量（768维）作为情绪因子
    \end{itemize}
  \end{block}

  \vspace{0.1cm}
  \textbf{示例代码}：
  \begin{lstlisting}[language=Python]
tokenizer = BertTokenizer.from_pretrained('bert-base-chinese')
model = BertModel.from_pretrained('bert-base-chinese', num_labels=3)

# 输入文本
text = "公司第三季度盈利超预期"
inputs = tokenizer(text, return_tensors='pt', padding=True, truncation=True)

# 获取 BERT 输出
with torch.no_grad():
    outputs = model(**inputs)
    cls_embedding = outputs.last_hidden_state[:, 0, :]  # 提取 [CLS] 的 768 维嵌入

# 将该向量直接输入到下游金融模型（如 XGBoost、神经网络）
# 与其他因子结合，预测股票收益或风险。
...
  \end{lstlisting}
\end{frame}

%------------------------------------------------