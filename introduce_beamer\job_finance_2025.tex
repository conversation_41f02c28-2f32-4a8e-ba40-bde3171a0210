\documentclass{ctexbeamer}
\mode<presentation> {
  \usetheme{Madrid}
  \usecolortheme{rose}
}

% 包的加载顺序很重要
\usepackage[T1]{fontenc}
\usepackage{amsmath}
\usepackage{graphicx}
\usepackage{adjustbox}
\usepackage{float}
\usepackage{booktabs}

% TikZ 相关包
\usepackage{tikz}
\usepackage{tikz-3dplot}
\usepackage{tikzscale}
\usetikzlibrary{positioning, shapes, shadows, arrows, calc, decorations.pathreplacing, matrix, fit, backgrounds}
\usetikzlibrary{chains, shapes.symbols, shapes.geometric, arrows.meta}

% pgfplots
\usepackage{pgfplots}
\pgfplotsset{compat=1.18}

% 代码高亮包
\usepackage{xcolor}
\usepackage{listings}

% 定义更多颜色
\definecolor{codegreen}{rgb}{0,0.6,0}
\definecolor{codegray}{rgb}{0.5,0.5,0.5}
\definecolor{codepurple}{rgb}{0.58,0,0.82}
\definecolor{backcolour}{rgb}{0.95,0.95,0.92}
\definecolor{bordercolor}{rgb}{0.8,0.8,0.8}
\definecolor{keywordcolor}{rgb}{0.0,0.5,1.0}
\definecolor{stringcolor}{rgb}{0.8,0.2,0.2}
\definecolor{commentcolor}{rgb}{0.4,0.7,0.4}

% 配置 listings 样式 - 移除无效的参数
\lstdefinestyle{mystyle}{
    backgroundcolor=\color{backcolour},
    commentstyle=\color{commentcolor}\itshape,
    keywordstyle=\color{keywordcolor}\bfseries,
    numberstyle=\tiny\color{codegray},
    stringstyle=\color{stringcolor},
    basicstyle=\ttfamily\tiny,           % 改为 tiny 更小的字体
    breakatwhitespace=false,
    breaklines=true,
    captionpos=b,
    keepspaces=true,
    numbers=left,
    numbersep=5pt,                   % 减小行号间距
    showspaces=false,
    showstringspaces=false,
    showtabs=false,
    tabsize=2,
    frame=single,
    frameround=tttt,
    framesep=2pt,                    % 减小框架间距
    rulecolor=\color{bordercolor},
    xleftmargin=6pt,                 % 减小左边距
    xrightmargin=6pt,                % 减小右边距
    aboveskip=8pt,                   % 减小上方间距
    belowskip=8pt,                   % 减小下方间距
    lineskip=-1pt                    % 减小行间距
}

% 应用样式
\lstset{style=mystyle}

% 设置全局行距压缩（在listings环境外使用）
\renewcommand{\baselinestretch}{0.95}

% Python 语言特定配置
\lstdefinelanguage{Python}{
    keywords={and,as,assert,break,class,continue,def,del,elif,else,except,exec,finally,for,from,global,if,import,in,is,lambda,not,or,pass,print,raise,return,try,while,with,yield,True,False,None},
    keywordstyle=\color{keywordcolor}\bfseries,
    ndkeywords={self,cls,@property,@staticmethod,@classmethod},
    ndkeywordstyle=\color{codepurple}\bfseries,
    identifierstyle=\color{black},
    sensitive=true,
    comment=[l]{\#},
    string=[s]{'}{'},
    string=[s]{"}{"},
    string=[s]{'''}{'''},
    string=[s]{"""}{"""},
    morestring=[b]',
    morestring=[b]"
}

\usepackage[bookmarks=true]{hyperref}

% 字体配置
\setCJKmainfont{SimSun}[AutoFakeBold=true]
\setCJKsansfont{SimHei}[AutoFakeBold=true]
\setCJKmonofont{SimSun}[AutoFakeBold=true]

%-----------------------------------
%	以下为正文
%-----------------------------------

\title[量子计算与金融科技]{Quantum Finance}
\author{张晓旭}
\institute[建信金科]
{
  CCB Fintech Co. Ltd. \\
  \medskip
  \textit{<EMAIL>}
}
\date{2025年06月17日}

\begin{document}

%-----------------------------------
%	标题页
%-----------------------------------
\begin{frame}
  \titlepage
\end{frame}

%-----------------------------------
%	目录
%-----------------------------------
\begin{frame}
  \frametitle{目录}
  % \begin{itemize}
  %   \item[1.] 量子算力在金融科技中的应用
  %   \item[2.] 金融AI云平台
  %   \item[3.] 大语言模型智能体的应用
  % \end{itemize}
  \tableofcontents
\end{frame}

%-----------------------------------
%	正文
%-----------------------------------
\section{量子算力在金融科技中的应用}
\input{frames/quantum-computing-chips.tex}

\subsection{量子投资组合优化算法}
\input{frames/algorithm-NISQ-QAOA.tex}

\subsection{量子期权定价算法}
\input{frames/quantum-option-pricing.tex}

\subsection{量子贝叶斯网络算法}
\input{frames/application-Finq-Bayesian.tex}

\subsection{量子风险价值算法}
\input{frames/quantum-VaR.tex}

\subsection{量子卷积神经网络算法}
\input{frames/qcnn.tex}

\subsection{量子长短期记忆网络算法}
\input{frames/algorithm-sequential-stock-QLSTM.tex}

\section{金融AI云平台}
\input{frames/AI-quant.tex}

\subsection{非结构化数据提取特征}
\input{frames/finance-data.tex}

\subsection{情绪因子挖掘模型}
\input{frames/fine-tune-bert.tex}

\subsection{CNN+Transformer预测模型}
\input{frames/algorithm-sequential-stock-transformer.tex}

\subsection{市场风险及估值计算系统}
\input{frames/market-risk-valuation-engine.tex}
\input{frames/market-risk-valuation.tex}
\input{frames/market-risk-valuation-workflow.tex}

\subsection{含权债券定价的BDT模型}
\input{frames/BDT-model.tex}
\input{frames/binary-tree-nodes.tex}

\subsection{预期尾部损失(ES)风险指标}
\input{frames/expected-short-fall.tex}
\input{frames/compare-VaR-ES.tex}

\section{大语言模型智能体的应用}

\subsection{设计文档审核智能体}
\input{frames/agent-browser-use.tex}
\input{frames/agent-browser-use-webform.tex}

\subsection{专利申办审核智能体}
\input{frames/agent-autopatent.tex}
\input{frames/agent-autopatent-output.tex}
\input{frames/agent-autopatent-generation.tex}


%-----------------------------------
%	结束页面
%-----------------------------------
\begin{frame}[plain]
  \vspace{1cm}
  \begin{center}
    {\Huge\bfseries Thank you all!}

    \vspace{1.5cm}
    \begin{tikzpicture}
      \node[rectangle, draw=gray, fill=gray!10, drop shadow, rounded corners=7pt, text width=0.7\textwidth, align=center, inner sep=12pt]
      {\Large\itshape Algorithms make the future promising.};
    \end{tikzpicture}

    \vspace{1cm}
    {\large\color{gray} -- 张晓旭, 2025年06月17日}
  \end{center}
\end{frame}

\end{document}