\documentclass{standalone}
\usepackage{tikz}
\usetikzlibrary{shapes.geometric, arrows.meta, positioning, fit, backgrounds, shadows}
\usepackage{cmbright}

\begin{document}

\begin{tikzpicture}[
        box/.style={
                rectangle, draw=black!80, rounded corners=4pt,
                minimum height=2.2em, minimum width=6em,
                align=center, font=\sffamily\footnotesize\bfseries, fill=white, drop shadow
            },
        arrow/.style={-Stealth, thick, draw=black!80},
        res-arrow/.style={-Stealth, thick, draw=blue!60, dashed},
        enc-box/.style={box, fill=blue!10},
        dec-box/.style={box, fill=orange!10},
        label/.style={font=\scriptsize, align=center},
        node distance=0.9cm and 1.5cm
    ]

    % Encoder Stack
    \node[enc-box] (input) {Input\\Embedding};
    \node[enc-box, above=of input] (pos-enc) {Positional\\Encoding};
    \node[enc-box, above=of pos-enc] (enc1) {Multi-Head\\Attention};
    \node[enc-box, above=of enc1] (add-norm1) {Add \& Norm};
    \node[enc-box, above=of add-norm1] (ffn) {Feed\\Forward};
    \node[enc-box, above=of ffn] (add-norm2) {Add \& Norm};
    \node[above=0.5cm of add-norm2] (enc-dots) {$\vdots$};
    \node[enc-box, above=0.5cm of enc-dots] (encN) {Encoder\\Layer $N$};

    % Decoder Stack
    \node[dec-box, right=4.2cm of input] (output) {Output\\Embedding};
    \node[dec-box, above=of output] (pos-dec) {Positional\\Encoding};
    \node[dec-box, above=of pos-dec] (dec1) {Masked\\Multi-Head\\Attention};
    \node[dec-box, above=of dec1] (add-norm3) {Add \& Norm};
    \node[dec-box, above=of add-norm3] (dec2) {Multi-Head\\Attention};
    \node[dec-box, above=of dec2] (add-norm4) {Add \& Norm};
    \node[dec-box, above=of add-norm4] (ffn-dec) {Feed\\Forward};
    \node[dec-box, above=of ffn-dec] (add-norm5) {Add \& Norm};
    \node[above=0.5cm of add-norm5] (dec-dots) {$\vdots$};
    \node[dec-box, above=0.5cm of dec-dots] (decN) {Decoder\\Layer $N$};
    \node[dec-box, above=of decN] (linear) {Linear};
    \node[dec-box, above=of linear] (softmax) {Softmax};

    % Arrows for Encoder
    \foreach \i/\j in {input/pos-enc, pos-enc/enc1, enc1/add-norm1, add-norm1/ffn, ffn/add-norm2, add-norm2/enc-dots, enc-dots/encN}{
            \draw[arrow] (\i) -- (\j);
        }

    % Residual Connections for Encoder
    \foreach \from/\to in {pos-enc/enc1, enc1/add-norm1, add-norm1/ffn, ffn/add-norm2}{
            \draw[res-arrow] (\from.north) to[out=90, in=-90] ([xshift=-1cm] \to.south);
        }

    % Arrows for Decoder
    \foreach \i/\j in {output/pos-dec, pos-dec/dec1, dec1/add-norm3, add-norm3/dec2, dec2/add-norm4,
            add-norm4/ffn-dec, ffn-dec/add-norm5, add-norm5/dec-dots, dec-dots/decN,
            decN/linear, linear/softmax}{
            \draw[arrow] (\i) -- (\j);
        }

    % Residual Connections for Decoder
    \foreach \from/\to in {pos-dec/dec1, dec1/add-norm3, add-norm3/dec2, dec2/add-norm4,
            add-norm4/ffn-dec, ffn-dec/add-norm5}{
            \draw[res-arrow] (\from.north) to[out=90, in=-90] ([xshift=1cm] \to.south);
        }

    % Encoder-Decoder Connection
    \draw[arrow, draw=purple!60, very thick] (encN) -| node[pos=0.75, above, label, text=purple!80] {Encoder Output} (dec2);

    % Grouping and Labels
    \begin{scope}[on background layer]
        \node[fit=(input)(pos-enc)(encN)(enc-dots), draw, rounded corners=10pt, fill=blue!5, inner sep=0.4cm] (enc-fit) {};
        \node[fit=(output)(pos-dec)(decN)(dec-dots)(linear)(softmax), draw, rounded corners=10pt, fill=orange!5, inner sep=0.4cm] (dec-fit) {};
        \node[above=0.15cm of enc-fit, label, font=\large\bfseries] {Encoder};
        \node[above=0.15cm of dec-fit, label, font=\large\bfseries] {Decoder};
    \end{scope}

\end{tikzpicture}

\end{document}
