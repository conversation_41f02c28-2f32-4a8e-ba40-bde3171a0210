%------------------------------------------------

\begin{frame}
  \frametitle{二叉树方法构建利率路径}
  
  \small
  \begin{columns}
    \begin{column}{0.5\textwidth}
      \begin{block}{利率二叉树示意图}
        \begin{figure}
          \centering
          \includegraphics[width=\textwidth]{figures/BDT-model-binary-tree.png}
        \end{figure}
      \end{block}
    \end{column}
    
    \begin{column}{0.45\textwidth}
      \begin{block}{计算步骤}
        \begin{enumerate}
          \item \textbf{时间设置}\\
          生成计息区间和利率重置日期
          
          \item \textbf{树结构构建}\\
          构建二叉树节点日期
          
          \item \textbf{现金流生成}\\
          生成债券现金流和行权现金流
          
          \item \textbf{参数计算}\\
          生成波动率和折现因子
          
          \item \textbf{树遍历}\\
          构建二叉树并计算节点价值
          
          \item \textbf{价格获取}\\
          从树根得到债券价格
        \end{enumerate}
      \end{block}
    \end{column}
  \end{columns}
  
  \vspace{0.2cm}
  \begin{center}
    \textit{通过利率二叉树实现含权债券的精确定价}
  \end{center}
\end{frame}

%------------------------------------------------