%%%%%%%%%%%%%%%
% This CV example/template is based on my own
% CV which I (lamely attempted) to clean up, so that
% it's less of an eyesore and easier for others to use.
%
% LianTze Lim (<EMAIL>)
% 23 Oct, 2022
% 24 Aug, 2024 -- Updated X (Twitter) icon
\documentclass[a4paper,skipsamekey,11pt,english]{curve}

% Uncomment to enable Chinese; needs XeLaTeX
\usepackage{ctex}


% Default biblatex style used for the publication list is APA6. If you wish to use a different style or pass other options to biblatex you can change them here.
\PassOptionsToPackage{style=ieee,sorting=ydnt,uniquename=init,defernumbers=true}{biblatex}

% Most commands and style definitions are in settings.sty.
\usepackage{settings}

% If you need to further customise your biblatex setup e.g. with \DeclareFieldFormat etc please add them here AFTER loading settings.sty. For example, to remove the default "[Online] Available:" prefix before URLs when using the IEEE style:
\DefineBibliographyStrings{english}{url={\textsc{url}}}

%% Only needed if you want a Publication List
\addbibresource{own-bib.bib}

%% Specify your last name(s) and first name(s) (as given in the .bib) to automatically bold your own name in the publications list.
%% One caveat: You need to write \bibnamedelima where there's a space in your name for this to work properly; or write \bibnamedelimi if you use initials in the .bib
% \mynames{Lim/Lian\bibnamedelima Tze}

%% You can specify multiple names like this, especially if you have changed your name or if you need to highlight multiple authors. See items 6–9 in the example "Journal Articles" output.
% \mynames{Lim/Lian\bibnamedelima Tze,
%   Wong/Lian\bibnamedelima Tze,
%   Lim/Tracy,
%   Lim/L.\bibnamedelimi T.}
%% MAKE SURE THERE IS NO SPACE AFTER THE FINAL NAME IN YOUR \mynames LIST
\myname{Zhang}{Xiao\bibnamedelima Xu}

% Change the fonts if you want
\ifxetexorluatex % If you're using XeLaTeX or LuaLaTeX
  \usepackage{fontspec}
  %% You can use \setmainfont etc; I'm just using these font packages here because they provide OpenType fonts for use by XeLaTeX/LuaLaTeX anyway
  \usepackage[p,osf,swashQ]{cochineal}
  \usepackage[medium,bold]{cabin}
  \usepackage[varqu,varl,scale=0.9]{zi4}
\else % If you're using pdfLaTeX or latex
  \usepackage[T1]{fontenc}
  \usepackage[p,osf,swashQ]{cochineal}
  \usepackage{cabin}
  \usepackage[varqu,varl,scale=0.9]{zi4}
\fi

% Change the page margins if you want
% \geometry{left=1cm,right=1cm,top=1.5cm,bottom=1.5cm}

% Change the colours if you want
% \definecolor{SwishLineColour}{HTML}{00FFFF}
% \definecolor{MarkerColour}{HTML}{0000CC}

% Change the item prefix marker if you want
% \prefixmarker{$\diamond$}

%% Photo is only shown if "fullonly" is included
\includecomment{fullonly}
% \excludecomment{fullonly}


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%


\leftheader{%
  {\LARGE\bfseries\sffamily 张晓旭, 物理学博士}

  \makefield{\faMobile[regular]}{\texttt{18910752106}}

  \makefield{\faEnvelope[regular]}{\href{mailto:<EMAIL>}{\texttt{<EMAIL>}}}
  % fontawesome5 doesn't have the X icon so we use
  % the simpleicons package here instead; but some
  % font size adjustment might be needed
  \makefield{\faGithub}{\url{https://github.com/longwarriors}}

  %% Next line
  % \makefield{\faGlobe}{\url{http://example.example.org/}}
  % You can use a tabular here if you want to line up the fields.
}

\rightheader{~}
\begin{fullonly}
\photo[r]{photo}
\photoscale{0.13}
\end{fullonly}

\title{Curriculum Vitae}

\begin{document}
\makeheaders[c]

\makerubric{education}
\makerubric{employment}
\makerubric{projects}

% If you're not a researcher nor an academic, you probably don't have any publications; delete this line.
%% Sometimes when a section can't be nicely modelled with the \entry[]... mechanism; hack our own and use \input NOT \makerubric
\input{publications}

% \makerubric{skills}
% \makerubric{misc}

% \makerubric{referee}
% \input{referee-full}

\end{document}