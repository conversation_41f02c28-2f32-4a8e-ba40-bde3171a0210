%------------------------------------------------

\begin{frame}
  \frametitle{市场风险及估值计算系统工作流程}
  
  \small
  \begin{columns}    
    \begin{column}{0.65\textwidth}
      \begin{block}{工作流程图}
        \begin{figure}
          \centering
          \begin{tikzpicture}[
            node distance=0.8cm,
            box/.style={rectangle, draw, fill=blue!20, text width=2.2cm, text centered, minimum height=0.6cm, font=\tiny},
            arrow/.style={->, thick}
          ]
            % 数据准备层
            \node[box, fill=green!20] (pos) {头寸数据};
            \node[box, fill=green!20, right=of pos] (market) {市场数据};
            \node[box, fill=green!20, below=0.4cm of pos.south east] (preprocess) {数据预处理};
            \node[box, fill=green!20, left=of preprocess] (hist) {历史数据};
            
            % 估值引擎层
            \node[box, fill=blue!20, below=0.6cm of preprocess] (val_engine) {估值引擎};
            \node[box, fill=blue!20, below=of val_engine] (valuation) {投资组合估值};
            
            % 风险引擎层
            \node[box, fill=red!20, below=0.6cm of valuation] (risk_engine) {风险计量引擎};
            \node[box, fill=red!20, left=of risk_engine] (scenario) {情景生成};
            \node[box, fill=red!20, below=of risk_engine] (es_calc) {ES风险指标};
            
            % 连接线
            \draw[arrow] (pos) -- (preprocess);
            \draw[arrow] (market) -- (preprocess);
            \draw[arrow] (preprocess) -- (val_engine);
            \draw[arrow] (val_engine) -- (valuation);
            \draw[arrow] (valuation) -- (risk_engine);
            \draw[arrow] (hist) -- (scenario);
            \draw[arrow] (scenario) -- (risk_engine);
            \draw[arrow] (risk_engine) -- (es_calc);
          \end{tikzpicture}
        \end{figure}
      \end{block}
    \end{column}
      
    \begin{column}{0.3\textwidth}
      \begin{alertblock}{核心特点}
        \begin{itemize}
          \item \textbf{模块化设计}\\
          独立的估值与风险引擎
          
          \item \textbf{并行计算}\\
          支持大规模组合处理
          
          \item \textbf{标准化}\\
          符合巴塞尔III监管要求
          
          \item \textbf{高效性}\\
          端到端计算流程
        \end{itemize}
      \end{alertblock}
    \end{column}
  \end{columns}  

\end{frame}

%------------------------------------------------