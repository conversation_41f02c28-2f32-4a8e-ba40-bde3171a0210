%------------------------------------------------

%------------------------------------------------
% 第一页：算法原理和优势
\begin{frame}
  \frametitle{量子期权定价算法}

  \small
  \begin{columns}
    \begin{column}{0.4\textwidth}
      \begin{block}{算法流程}
        \begin{enumerate}
          \item \textbf{量子态制备}:
                $$|\psi\rangle = \sum_{i=0}^{2^n-1} \sqrt{p_i}|i\rangle$$
          \item \textbf{收益函数映射}:
                $$f(S_T) = \max(0, S_T - K)$$
          \item \textbf{振幅估计}:
                $$\mathbb{E}[f] \approx \sin^2(\pi\theta)$$
          \item \textbf{期权定价}:
                $$V_0 = e^{-rT}\mathbb{E}[f]$$
        \end{enumerate}
      \end{block}
    \end{column}

    \begin{column}{0.5\textwidth}
      \begin{block}{核心思想}
        \begin{itemize}
          \item 使用量子振幅编码对数正态分布
          \item 通过分段线性函数构建期权收益
          \item 迭代量子振幅估计(IQAE)获取期权价值
        \end{itemize}
      \end{block}

      \begin{alertblock}{技术优势}
        \begin{itemize}
          \item \textcolor{black}{\textbf{量子并行性}}: 2$^n$个状态同时计算
          \item \textcolor{black}{\textbf{采样效率}}: 更少的采样点达到相同精度
          \item \textcolor{black}{\textbf{内存节省}}: 指数级压缩数据存储
        \end{itemize}
      \end{alertblock}
    \end{column}
  \end{columns}

  \vspace{0.2cm}
  \begin{center}
    \textit{利用量子叠加态实现高效期权估值}
  \end{center}
\end{frame}

%------------------------------------------------
% 第二页：实验结果
\begin{frame}
  \frametitle{量子期权定价算法 - 实验结果}

  \begin{columns}
    \begin{column}{0.55\textwidth}
      \begin{block}{定价结果对比}
        \centering
        \begin{tabular}{|l|c|c|}
          \hline
          \textbf{方法}                    & \textbf{期权价格}                    & \textbf{相对误差}                    \\
          \hline
          BSM解析解                         & 0.0398                           & -                                \\
          经典蒙特卡罗                         & 0.0392                           & 1.51\%                           \\
          \textcolor{red}{\textbf{量子算法}} & \textcolor{red}{\textbf{0.0396}} & \textcolor{red}{\textbf{0.50\%}} \\
          \hline
        \end{tabular}
      \end{block}

      \begin{block}{计算参数设置}
        \begin{itemize}
          \item \textbf{标的价格}: S₀ = 100
          \item \textbf{执行价格}: K = 105
          \item \textbf{到期时间}: T = 0.25年
          \item \textbf{无风险利率}: r = 5\%
          \item \textbf{波动率}: σ = 20\%
        \end{itemize}
      \end{block}
    \end{column}

    \begin{column}{0.38\textwidth}
      \begin{alertblock}{量子计算参数}
        \begin{itemize}
          \item \textbf{量子比特数}: 4 qubits
          \item \textbf{采样次数}: 16 samples
          \item \textbf{置信度}: 95\%
          \item \textbf{IQAE迭代}: 5次
        \end{itemize}
      \end{alertblock}

      \begin{block}{性能优势}
        \begin{itemize}
          \item 精度提升: 相对误差降低66\%
          \item 计算效率: 采样次数减少75\%
          \item 内存占用: 指数级压缩
        \end{itemize}
      \end{block}
    \end{column}
  \end{columns}

  \vspace{0.2cm}
  \begin{center}
    \footnotesize\textit{量子算法在期权定价中展现出更高的精度和效率}
  \end{center}
\end{frame}

%------------------------------------------------

%------------------------------------------------