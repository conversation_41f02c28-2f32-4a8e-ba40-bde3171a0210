%-----------------------------------
%-----------------------------------
% Beamer template made by Chenxiao
% 2023.11.04 at Qingdao,China
%-----------------------------------
%-----------------------------------

\documentclass{beamer}

\mode<presentation> {
    \usetheme{Madrid}
    \usecolortheme{rose}
    
    %\setbeamertemplate｛footline｝
    %若要删除所有幻灯片中的页脚，请取消注释此行
    
    %\setbeamertemplate｛footline｝[页码]
    %若要用简单的幻灯片计数替换所有幻灯片中的页脚，请取消注释此行
    
    %\setbeamertemplate｛导航符号｝｛｝
    %要删除所有幻灯片底部的导航符号，请取消注释此行
}
\usepackage{listings}      % 用于插入代码
\usepackage{xcolor}        % 用于语法高亮颜色
% 设置 listings 参数
\lstset{
    basicstyle=\ttfamily\small,
    keywordstyle=\color{blue},
    commentstyle=\color{gray},
    stringstyle=\color{red},
    frame=single,
    breaklines=true,
    numbersep=10pt,     % 行号与代码之间的距离，增大此值
    tabsize=4,          % 每个制表符占 4 个空格
    showtabs=false,     % 不显示 ^^I
    showspaces=false,   % 不显示空格标记
    keepspaces=true,    % 保留原始空格
    linewidth=0.6\textwidth, % 限制宽度为页面宽度的 90%
    breaklines=true,         % 启用自动换行
}
\usepackage[T1]{fontenc}   % 使用 T1 字体编码
\usepackage{graphicx}      % 允许包含图像
\usepackage{tikz}          % TikZ 绘图库
\usepackage{quantikz}      % 量子电路绘制
\usepackage{adjustbox}     % 适用于 resizebox
\usepackage{pgfplots}      % 绘制数学图形（可能用到）
\pgfplotsset{compat=1.18}  % 设置 pgfplots 兼容模式
\usepackage{amsmath}       % 数学符号支持
\usepackage{float}         % 允许控制 figure 位置
\usepackage{tikz-3dplot}   % 可能用于 3D 画图
\usepackage{tikzscale}     % 让 TikZ 图形可缩放
\usepackage{booktabs}      % 允许在表中使用\toprule、\ midrule和\ bottomrule
\usepackage{ctex, hyperref} % 中文支持
\usepackage[bookmarks=true]{hyperref}
\usetikzlibrary{positioning, shapes, arrows, calc, decorations.pathreplacing, matrix, fit, backgrounds} % 额外 TikZ 库
\usetikzlibrary{chains, shapes.symbols, shapes.geometric, arrows.meta} % 使用更丰富的箭头样式

%-----------------------------------
%	以下为正文
%-----------------------------------
% 简短标题显示在每张幻灯片的底部，完整标题仅在标题页上
\title[量子技术的影响与应用]{Quantum Technology: Computing and Sensing} 
\author{张晓旭} % Your name
\institute[CCBFT] % 您的机构将出现在每张幻灯片的底部，可能是节省空间的简写
{
    CCB Fintech Co. Ltd. \\ % 你所在的机构
    \medskip
    \textit{<EMAIL>} % Your email address
}
\date{\today} % 日期，可以更改为自定义日期

\begin{document}
    %------------------------------------------------

    \begin{frame}
        \titlepage % 将标题页打印为第一张幻灯片
    \end{frame}

    %------------------------------------------------

    \begin{frame}
        \frametitle{Overview} % 目录幻灯片，注释此块以将其删除
        \tableofcontents % 在整个演示过程中，如果您选择使用\ section｛｝和\ submission｛}命令，这些命令将自动打印在此幻灯片上，作为演示的概述
    \end{frame}
    
    %-----------------------------------
    %	开始创建PPT正文内容
    %-----------------------------------
    
    \section{Quantum computing} % 可以创建章节，以便将您的演讲组织成离散的块，所有章节和小节都会自动打印在目录中，作为演讲的概述
    \subsection{Hardware} % 可以在一组具有共同主题的幻灯片之前创建一个小节，以进一步将您的演示分解为块

    % \begin{frame}{量子计算芯片示例}
    %     \begin{columns}[b] % [T] 表示顶部对齐
    %         \column{0.33\textwidth} % 每个列占 1/3 宽度
    %         \centering
    %         \includegraphics[width=0.9\textwidth]{figures/chip-IBM.png} % 替换为实际文件名
    %         \textbf{IBM} \\
    %         \textbf{127 Qubits}
            
    %         \column{0.33\textwidth}
    %         \centering
    %         \includegraphics[width=0.9\textwidth]{figures/chip-Google.png} % 替换为实际文件名
    %         \textbf{Google} \\
    %         \textbf{53 Qubits}
            
    %         \column{0.33\textwidth}
    %         \centering
    %         \includegraphics[width=0.9\textwidth]{figures/chip-IonQ.png} % 替换为实际文件名
    %         \textbf{IonQ} \\
    %         \textbf{32+ Qubits}
    %     \end{columns}
    % \end{frame}

    %------------------------------------------------

    % \begin{frame}{超导量子计算机电气结构}			
    %     \textit{超导量子计算机依赖于超导量子比特，通常基于约瑟夫森结（Josephson Junction）。
    %     需要在极低温度下运行（通常接近绝对零度，~10-20 mK），
    %     由稀释制冷机（dilution refrigerator）冷却至毫开尔文级别，以保持超导状态并减少热噪声。}
    %     \begin{columns}[c] % [t] 表示顶部对齐，可选
    %         \column{0.45\linewidth}
    %         \raggedright
    %         \includegraphics[width=0.9\linewidth]{figures/fridge.jpeg}
    %         \column{0.55\linewidth}
    %         \raggedleft
    %         \includegraphics[width=0.8\linewidth]{figures/quantum-computer-electrical.png}			
    %     \end{columns}
    % \end{frame}
    
    %------------------------------------------------

    % \begin{frame}{超导量子比特的电气模型}			
    %     \textit{现有的先进制程微纳加工和微波集成电路技术,可以直接应用于超导量子处理器。总体来说，加工的元件有两种。}
    %     \begin{itemize}
    %         \item 线性振荡元件：共面波导谐振腔 \textit{CPW resonator}
    %         \item 非线性振荡元件：超导量子比特 \textit{Superconducting qubit}
    %     \end{itemize}
    %     \begin{columns}[c] % [t] 表示顶部对齐，可选
    %         \column{0.45\linewidth}
    %         \raggedright
    %         \includegraphics[width=0.9\linewidth]{figures/qubit-circuits.png}
    %         \column{0.55\linewidth}
    %         \raggedleft
    %         \includegraphics[width=0.8\linewidth]{figures/LC-oscillator-the-phase.png}			
    %     \end{columns}
    % \end{frame}
    
    %------------------------------------------------

    % \begin{frame}{超导量子比特的物理模型}			
    %     \textit{宏观量子效应——超导约瑟夫森节器件的物理模型。}
    %     \begin{align}
    %         I(t) &= I_{c}\sin(\varphi(t)) \label{eq:current} \\
    %         \frac {\partial \varphi(t)}{\partial t} &= {\frac {2eV(t)}{\hbar}} \label{eq:voltage}
    %     \end{align}
    %     \vspace{8pt} % 增加间距
    %     \begin{columns}[c] % [t] 表示顶部对齐，可选
    %         \column{0.55\linewidth} % 第一列宽度为页面宽度的 50%
    %         \raggedleft
    %         \includegraphics[width=\linewidth]{figures/superconduct-copper-pairs.png} % 第一张图片
    %         \column{0.45\linewidth} % 第二列宽度为页面宽度的 50%
    %         \raggedleft
    %         \includegraphics[width=\linewidth]{figures/superconduct-junction-psi.png} % 第二张图片
    %     \end{columns}		
    % \end{frame}
    
    %------------------------------------------------

    % \begin{frame}{量子比特仿真}			
    %     \textit{开放量子系统的动力学演化，可以通过量子比特哈密顿量进行模拟。}
    %     \begin{equation}
    %         \label{eq:vsphere}
    %         H=4E_\text{C}(\hat{n}-n_g)^2-\frac{1}{2}E_\text{J}\sum_n(|n\rangle\langle n+1|+\text{h.c.}),
    %     \end{equation}
    %     \vspace{3pt} % 增加间距
    %     \begin{figure}
    %         \includegraphics[width=0.7\linewidth]{figures/transmon.png}
    %     \end{figure}	
    % \end{frame}
    
    %------------------------------------------------
    % \subsection{Algorithms}

    % \begin{frame}{NISQ algorithms VQE}		
    %     \textit{硬件高效变分量子态（Hardware Efficient Ansatz）是一种专门为 NISQ 量子计算机设计的量子电路结构，用于变分量子算法（如变分量子本征求解器 VQE, Variational Quantum Eigensolver）。}
    %     \vspace{20pt} % 增加间距
    %     \begin{columns}[c] % [t] 表示顶部对齐，可选
    %         \column{0.4\linewidth} % 第一列宽度为页面宽度的 50%
    %         \raggedleft
    %         \includegraphics[width=\linewidth]{figures/pluse-based-qubits.png} % 第一张图片
    %         \column{0.6\linewidth} % 第二列宽度为页面宽度的 50%
    %         \raggedleft
    %         \includegraphics[width=\linewidth]{figures/VQE.png} % 第二张图片
    %     \end{columns}
    % \end{frame}
    
    %------------------------------------------------

    % \begin{frame}{NISQ algorithms QAOA}		
    %     \textit{量子近似优化算法（Quantum Approximate Optimization Algorithm）是一种基于量子电路的二次优化（Quadratic Optimization），
    %     用于求解金融中的投资组合优化问题。基于马科维茨均值-方差模型（Markowitz Mean-Variance Analysis）。}
    %     \vspace{8pt} % 增加间距
    %     \begin{columns}[T] % T 表示顶部对齐
    %         % 左半部分
    %         \begin{column}{0.48\textwidth}
    %             \raggedleft
    %               \textbf{经典优化的马科维兹模型}
    %               \vspace{0.5cm}
    %             \begin{align}
    %                 \min \quad & w^T \Sigma w \\
    %                 \text{s.t.} \quad & \mu^T w \geq r \\
    %                 & \sum w = 1
    %             \end{align}
    %         \end{column}
            
    %         % 右半部分
    %         \begin{column}{0.48\textwidth}
    %             \raggedleft
    %             \textbf{量子优化的马科维兹模型}
    %             \vspace{0.5cm}
    %             \begin{align}
    %                 \min \quad & \frac{1}{2} q w^T \Sigma w - \mu^T w \\
    %                 \text{s.t.} \quad & \sum w = 1
    %             \end{align}
    %             \end{column}
    %         \end{columns}

    % \end{frame}
    
    %------------------------------------------------
    \subsection{FintechQ}
    
    % \begin{frame}{量子金融云服务}		
    %     \textit{量子计算机需要超低温和精确的校准，维护难度大。QaaS（量子即服务）通过云服务形式提供量子算力，极大降低了量子计算的使用门槛。建行云由专业团队负责硬件的运行和优化，确保设备处于最佳状态。}
    %     \vspace{6pt} % 增加间距
    %     \begin{figure}
    %         \includegraphics[width=0.7\linewidth]{figures/qps-client.png}
    %     \end{figure}
    % \end{frame}
    
    %------------------------------------------------

    % \begin{frame}{量子金融风险管理}		
    %     \textit{在信用风险管理中，“回收率”通常是指在债务人违约（default）后，债权人能够从债务人追回的资金占原始债务本金的百分比\footnote{贷款100万元，抵押物价值在违约后涨到120万元并被全额收回，那么回收率就达到了120\%}。传统的风险预测假设回收率固定（如40\%），量子机器学习能给出回收率分布。}
    %     \begin{block}{Recovery Rate}{
    %         \centerline{$\text{回收率} = \frac{\text{违约后回收的金额}}{\text{违约时的债务本金}} \times 100\%$}
    %         }
    %     \end{block}
    % \end{frame}
    
    %------------------------------------------------

    \begin{frame}{量子神经网络分类器}
        \textit{量子神经网络对债券违约与否进行分类预测，量子态编码的高维特征提高了模型的泛化能力。}
        \vspace{0.75cm} % 调整间距
        \begin{columns}[c] % [t] 表示顶部对齐，可选
            \column{0.7\linewidth} % 第一列宽度为页面宽度的 50%
            \raggedleft
            \includegraphics[width=\linewidth]{figure-AmplitudeEncoding.pdf} % 第一张图片
            \column{0.35\linewidth} % 第二列宽度为页面宽度的 50%
            \raggedleft
            \includegraphics[width=\linewidth]{figures/recovery_rate_stat.pdf} % 第二张图片
        \end{columns}
    \end{frame}
    
    %------------------------------------------------

    % \begin{frame}{量子金融特征选取}
    %     \textit{将低维数据嵌入（embedding）到高维希尔伯特空间，从而提升模型的泛化能力。根据信用卡持卡人有限的个人信息，训练生成高维量子态作为新特征，提高违约模型的分类准确率。}
    %     \vspace{6pt} % 增加间距
    %     \begin{figure}
    %         \includegraphics[width=0.5\linewidth]{figures/high-dimensional-feature-space.png}
    %     \end{figure}
    % \end{frame}

    %------------------------------------------------
    
    % \begin{frame}{量子金融时序预测}
    %     \textit{量子长短时记忆网络（QLSTM）是一种适用于时序数据的量子神经网络，可以捕捉金融市场交易价量数据的长期依赖关系。}
    %     \vspace{6pt} % 增加间距
    %     \begin{figure}
    %         \includegraphics[width=0.65\linewidth]{figures/myQLSTM.png}
    %     \end{figure}
    % \end{frame}

    %------------------------------------------------

    % \begin{frame}{量子计算巨大投入}
    %     \textit{“需要很多数学、物理跟工程全部合在一起的大型的一些研究，量子计算这个学科比如来讲，在IBM一个公司，就有1200人在做。1200个科技人才，工程师跟做基本科研的。
    %     一个工程师的这个费用，至少要20万美金以上，平均起来，有些可能更多。你就想想看，你乘上1200个人，就是一大笔钱，有几亿来算的。
    %     那只是一个公司，还有Google，还有Microsoft，还有很多公司，还有大学里面的投资。哈佛大学最近投资3亿美金，就为了做那个量子计算，里面有很好本来很好的大学教授都在做。中国很少人在做。”		
    %     }	
    %     \rightline{\textit{——丘成桐，2020年9月13日，凤凰卫视“名人面对面” }}
    % \end{frame}
    
    %------------------------------------------------    
    
    \begin{frame}
        \frametitle{Bullet Points}
        \begin{itemize}
            \item What we do may be small, but it has a certain character of permanence.
            \item Euclid geometry was as dazzling as first love.
            \item Talk is cheap, solve the PDE.
        \end{itemize}
    \end{frame}
    
    %------------------------------------------------
    
    \begin{frame}[fragile]{Python代码块}
    \begin{lstlisting}[language=Python, basicstyle=\ttfamily\scriptsize]
import torchquantum as tq
import torchquantum.functional as tqf
import torch.nn as nn
import torch.nn.functional as F

class QuantumNeuralNetwork(nn.Module):
    def __init__(self):
        super().__init__()
        self.qlayer = tq.QuantumLayer(2, 2)
        self.fc = nn.Linear(2, 2)

    def forward(self, x):
        x = tqf.state_to_density_matrix(x)
        x = self.qlayer(x)
        x = tqf.density_matrix_to_state(x)
        x = self.fc(x)
        return F.log_softmax(x, dim=1)
        \end{lstlisting}
        \end{frame}
    
    %------------------------------------------------

    % \begin{frame}[fragile]
    %     \frametitle{Python 代码示例}
    %     \begin{block}{MATLAB代码}
    %     \begin{lstlisting}[language=Python]
    % def greet(name):    # 假设这里原先有制表符
    %     message = "Hello," + name + "!"
    %     print(message)
    % greet("Beamer")
    %     \end{lstlisting}
    %     \end{block}
    % \end{frame}

    %------------------------------------------------
    
    \begin{frame}
        \frametitle{Multiple Columns}
        \begin{columns}[c] % The "c" option specifies centered vertical alignment while the "t" option is used for top vertical alignment
            
            \column{.45\textwidth} % Left column and width
            \textbf{Heading}
            \begin{enumerate}
                \item Statement
                \item Explanation
                \item Example
            \end{enumerate}
            
            \column{.5\textwidth} % Right column and width
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer lectus nisl, ultricies in feugiat rutrum, porttitor sit amet augue. Aliquam ut tortor mauris. Sed volutpat ante purus, quis accumsan dolor.
            
        \end{columns}
    \end{frame}
    
    %------------------------------------------------
    \section{Quantum sensing}
    \subsection{Research implications} % 可以在一组具有共同主题的幻灯片之前创建一个小节，以进一步将您的演示分解为块
    
    % \begin{frame}{Neural Quantum States}
    %     神经网络超强的表示能力正在替代量子信息中的量子态表述\footnote{Sharir, O., Shashua, A., Carleo, G., 2022. Neural tensor contractions and the expressive power of deep neural quantum states. Phys. Rev. B 106, 205136. }，神经网络态可以很高效的描述量子性\textit{Quantumness}\footnote{Sinibaldi, A., Mello, A.F., Collura, M., Carleo, G., 2025. Non-stabilizerness of Neural Quantum States.}，即\textit{non-stabilizerness}与\textit{entanglement}		
    %     \begin{columns}[c] % [t] 表示顶部对齐，可选
    %         \column{0.4\textwidth} % 第一列宽度为页面宽度的 50%
    %         \includegraphics[width=0.9\textwidth]{figures/diagram-ExpressivePower.pdf} % 第一张图片
    %         \column{0.6\textwidth} % 第二列宽度为页面宽度的 50%
    %         \includegraphics[width=0.9\textwidth]{figures/NQSRepresentability.png} % 第二张图片
    %     \end{columns}
    % \end{frame}
    
    %------------------------------------------------
    
    \begin{frame}
        \frametitle{Table}
        \begin{table}
            \begin{tabular}{l l l}
                \toprule
                \textbf{Treatments} & \textbf{Response 1} & \textbf{Response 2}\\
                \midrule
                Treatment 1 & 0.0003262 & 0.562 \\
                Treatment 2 & 0.0015681 & 0.910 \\
                Treatment 3 & 0.0009271 & 0.296 \\
                \bottomrule
            \end{tabular}
            \caption{Table caption}
        \end{table}
    \end{frame}
    
    %------------------------------------------------

    \subsection{Commercial applications}

    \begin{frame}{Theorem}
        \begin{theorem}[Mass--energy equivalence]
        \centerline{$E = mc^2$}
        \end{theorem}
    \end{frame}

    %------------------------------------------------
    
    \begin{frame}[fragile] % Need to use the fragile option when verbatim is used in the slide
        \frametitle{Verbatim}
        \begin{example}[Theorem Slide Code]
            \begin{verbatim}
\begin{frame}
\frametitle{Theorem}
\begin{theorem}[Mass--energy equivalence]
$E = mc^2$
\end{theorem}
\end{frame}\end{verbatim}
        \end{example}
    \end{frame}
    
    %------------------------------------------------

    % \begin{frame}{量子传感物理过程}
    %     \begin{tikzpicture}[
    %     nodes={shape=signal, signal from=west, signal to=east, align=left,font=\sffamily, on chain, minimum height=2em, inner xsep=0.5em},
    %     start chain=going right,
    %     node distance=1ex]
    %    \path node[fill=gray!50]{初始\\热态} node[fill=gray!50]{非平衡\\极化态} node[fill=gray!50]{光与物质\\相互作用} node[fill=gray!50]{外场下\\态演化} node[fill=gray!50]{宏观态\\探测} ;
    %     \end{tikzpicture}
    %     \begin{figure}
    %     \includegraphics[width=0.8\linewidth]{figures/Qsensing-framework.png}
    %     % \caption{量子传感技术基本架构} % 可选标题
    %     \end{figure}
    % \end{frame}

    %------------------------------------------------

    % \begin{frame}{量子传感器性能}
    %     量子传感器性能的核心指标——噪声、灵敏度、精度、量程、带宽等
    %     \begin{figure}
    %     \includegraphics[width=0.8\linewidth]{figures/measure-process.png}
    %     % \caption{传感器含噪信号测量} % 可选标题
    %     \end{figure}
    % \end{frame}
    
    %------------------------------------------------
    
    % \begin{frame}{功率谱密度}
    %     描述有噪声的时变信号  
    %     \begin{columns}[T]
    %         \begin{column}{\linewidth}
    %             \centering
    %             \includegraphics[width=0.8\linewidth]{figures/psd-implementation.png}
    %         \end{column}
    %     \end{columns}
        
    %     \vspace{1cm} % 调整间距
        
    %     \begin{columns}[T]
    %         \begin{column}{\linewidth}
    %             \centering
    %             \includegraphics[width=0.8\linewidth]{figures/psd_plots.png}
    %         \end{column}
    %     \end{columns}
    % \end{frame}

    %------------------------------------------------

    \begin{frame}{Quantum Kernel Learning}    
        \textbf{量子核方法处理高维特征：}
        \textbf{Apply non-linear "feature map" \( x \rightarrow \Phi(x) \)}
        
        % 使用 TikZ 绘制图形
        \begin{tikzpicture}[scale=0.8]
            % 上层平面（灰色）
            \fill[gray!30] (0,0) -- (6,0) -- (7,1) -- (1,1) -- cycle;
            \draw[thick] (0,0) -- (6,0) -- (7,1) -- (1,1) -- cycle;
            
            % 绘制红点（上层）
            \foreach \i in {1,2,3,4,5}
                \fill[red] ({1.5 + 0.8*rand}, {0.5 + 0.4*rand}) circle (0.2);
            
            % 绘制蓝点（上层）
            \foreach \i in {1,2,3}
                \fill[blue] ({3.5 + 0.8*rand}, {0.5 + 0.4*rand}) circle (0.2);
            
            % 下层平面（白色，带边框）
            \draw[thick] (-1,-3) -- (8,-3) -- (9,-2) -- (0,-2) -- cycle;
            
            % 绘制红点（下层）
            \foreach \i in {1,2,3,4}
                \fill[red] ({1 + 0.8*rand}, {-2.5 + 0.4*rand}) circle (0.2);
            
            % 绘制蓝点（下层）
            \foreach \i in {1,2,3,5}
                \fill[blue] ({4 + 0.8*rand}, {-2.5 + 0.4*rand}) circle (0.2);
            
            % 绘制箭头（特征映射 \(\Phi\)）
            \draw[->, thick] (3.5,-0.5) -- (3.5,-2);
            \node at (3.5,-1.2) {$\Phi$};
        \end{tikzpicture}
        
        % 底部文本
        \textbf{Decision function}
        \[
        f(x) = W \cdot \Phi(x)
        \]
    \end{frame}

    %------------------------------------------------

    % \begin{frame}{Big Data Statistics}
    %     \textbf{数据增强与容错}		
    %     \begin{enumerate}
    %         \item 多源数据融合
    %         \item 特征提取
    %         \item 模式识别
    %     \end{enumerate}
    %     高精度、超灵敏的传感器是昂贵且不易部署的。 \\
    %     通过深度学习模型，是否可以把粗糙的、干扰的数据映射到更准确的估计值上？ \\
    %     单个维度上的低精度（不可区分），在高维空间是否可以区分？
    % \end{frame}

    %------------------------------------------------

    \begin{frame}{Kernel Learning}

    \begin{tikzpicture}[font=\large, line join=round, >=stealth, scale=1.0]

      %---------------------
      % 1. 在 2D 平面绘制坐标轴
      %---------------------
      \draw[->] (0,0) -- (3,0) node[right] {$x_1$};
      \draw[->] (0,0) -- (0,2) node[above] {$x_2$};

      %---------------------
      % 2. 在 2D 平面上绘制红、蓝点
      %---------------------
      % 红点
      \foreach \x/\y in {(0.5,0.5), (1.0,1.0), (1.2,0.8), (0.8,1.3)} {
        \fill[red] \x\y circle (2pt);
      }
      % 蓝点
      \foreach \x/\y in {(2.0,1.2), (2.2,0.9), (1.8,0.7), (2.3,1.4)} {
        \fill[blue] \x\y circle (2pt);
      }

      %---------------------
      % 3. 箭头表示非线性映射 \Phi
      %---------------------
      \draw[->, thick] (1.5,2.0) to[out=60, in=180] (3.5,4.0)
        node[midway, above] {$\Phi$};

      %---------------------
      % 4. 在 3D 空间（简化为斜矩形）绘制映射后的点
      %---------------------
      \begin{scope}[shift={(3.5,4.0)}] 
        % 绘制一个倾斜的平面
        \draw[fill=gray!20, opacity=0.5] (0,0) -- (2,0) -- (2.5,1) -- (0.5,1) -- cycle;

        % 在“平面之上”放一些红点（示意）
        \fill[red] (0.8,1.3) circle (2pt);
        \fill[red] (1.2,1.5) circle (2pt);
        \fill[red] (1.0,1.2) circle (2pt);

        % 在“平面之下”放一些蓝点（示意）
        \fill[blue] (1.0,0.2) circle (2pt);
        \fill[blue] (1.5,0.3) circle (2pt);
        \fill[blue] (1.2,0.4) circle (2pt);

        % 用一条线表示可分的超平面
        \draw[thick] (0.5,0.7) -- (2.3,0.7);
      \end{scope}

      %---------------------
      % 5. 决策函数 f(x) = W \cdot \Phi(x)
      %---------------------
      \node[draw, rectangle, rounded corners, fill=gray!20, 
            align=center] at (7.0,2.5) {\large $f(x) = W \cdot \Phi(x)$};

    \end{tikzpicture}

    \vspace{1em}
    \textbf{Apply non-linear ``feature map'' } $x \mapsto \Phi(x)$

    \end{frame}

    %------------------------------------------------

    \begin{frame}
        \Huge{\centerline{Thank you all！}}
        \centering{{\normalsize If you are not completely confused by quantum mechanics, you do not understand it.}}
    \end{frame}

    %------------------------------------------------

\end{document}
