%------------------------------------------------

\begin{frame}
  \frametitle{含权债券定价的BDT模型}  

    \begin{block}{含权债券定价}
        \textbf{可赎回债券}
        $$\text{Node}(i,j) = \min(\text{NPV}(i,j), K + \text{Acc}) + \text{cashflow}$$
        
        \vspace{0.2cm}
        \textbf{可回售债券}
        $$\text{Node}(i,j) = \max(\text{NPV}(i,j), K + \text{Acc}) + \text{cashflow}$$
        
        \vspace{0.2cm}
        \begin{itemize}
          \item \textcolor{blue}{NPV}: 净现值
          \item \textcolor{blue}{K}: 行权价格
          \item \textcolor{blue}{Acc}: 应计利息
        \end{itemize}
      \end{block}  

\end{frame}

%------------------------------------------------