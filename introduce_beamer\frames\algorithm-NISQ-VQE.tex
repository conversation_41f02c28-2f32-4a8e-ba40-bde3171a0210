\begin{frame}{NISQ algorithm: VQE}		
    \small
    \textit{硬件高效变分量子态（Hardware Efficient Ansatz）是一种专门为 NISQ 量子计算机设计的量子电路结构，用于变分量子算法（如变分量子本征求解器 VQE, Variational Quantum Eigensolver）。}
    \vspace{20pt} % 增加间距
    \begin{columns}[c] % [t] 表示顶部对齐，可选
        \column{0.4\linewidth} % 第一列宽度为页面宽度的 50%
        \raggedleft
        \includegraphics[width=\linewidth]{figures/pluse-based-qubits.png} % 第一张图片
        \column{0.6\linewidth} % 第二列宽度为页面宽度的 50%
        \raggedleft
        \includegraphics[width=\linewidth]{figures/VQE.png} % 第二张图片
    \end{columns}
\end{frame}