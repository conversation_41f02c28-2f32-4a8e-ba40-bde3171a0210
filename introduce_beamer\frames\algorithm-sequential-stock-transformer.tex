\begin{frame}[fragile]{基于Transformer的金融时间序列预测}
    \small
    \textit{基于GPT-3结构，只使用Transformer的decoder部分，采用自监督的方式进行小样本自回归训练。
    利用CNN捕捉短期依赖关系和Transformer学习长期依赖关系。
    实验证明，这种方法在预测标准普尔500指数成分股的日内股价变化方面优于传统的统计和深度学习方法。}\footnote{Zeng, Z., Kaur, R., Siddagangappa, S., Rahimi, S., Balch, T., Veloso, M., 2023. Financial Time Series Forecasting using CNN and Transformer. https://doi.org/10.48550/arXiv.2304.04912
    }
%     \begin{lstlisting}[language=Python, basicstyle=\ttfamily\scriptsize]       
% class StockTransformerDecoder(nn.Module):
%     def __init__(self, window_len, 
%                 num_layers, 
%                 input_size, 
%                 output_size, 
%                 d_model, 
%                 num_heads, 
%                 feedforward_dim, 
%                 dropout=0.1,
%                 positional_encoding="sinusoidal"):
%         super().__init__()
%         ...  
%     def init_weights(self):
%         ...    
%     def generate_mask(self, dim1, dim2):
%         ...        
%     def forward(self, src, src_mask=None):
%         ...
%     \end{lstlisting}
    \begin{figure}
        \includegraphics[width=0.7\linewidth]{figures/CTTS_Architecture.png}
    \end{figure}	
\end{frame}