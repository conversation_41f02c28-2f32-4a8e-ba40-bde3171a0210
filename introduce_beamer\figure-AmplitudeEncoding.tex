\documentclass{standalone}
\usepackage{graphicx}      % 处理图形
\usepackage{tikz}          % TikZ 绘图库
\usepackage{quantikz}      % 量子电路绘制
\usepackage{adjustbox}     % 适用于 resizebox
\usepackage{pgfplots}      % 绘制数学图形（可能用到）
\pgfplotsset{compat=1.18}  % 设置 pgfplots 兼容模式
\usepackage{amsmath}       % 数学符号支持
\usepackage{float}         % 允许控制 figure 位置
\usepackage{tikz-3dplot}   % 可能用于 3D 画图
\usepackage{tikzscale}     % 让 TikZ 图形可缩放
\usetikzlibrary{positioning, shapes, arrows, calc, decorations.pathreplacing, matrix, fit, backgrounds} % 额外 TikZ 库

\begin{document}
\begin{figure}
    \resizebox{1.\linewidth}{!}{
        \begin{tikzpicture}
            % Draw the TikZ neural network on the left
            \node[draw, thick, rectangle, fill=red!30] (box1) at (0,0) {
                \begin{tikzpicture}[
                neuron/.style={circle, draw, minimum size=0.8cm}, % Reduced size of neurons
                layer/.style={draw=none,fill=none},
                ->, >=stealth',
                dashedline/.style={draw=black, dashed}
                ]
        
                % Input layer nodes (4 input neurons)
                \node[neuron] (I1) at (0, 3) {};
                \node[neuron] (I2) at (0, 2) {};
                \node[neuron] (I3) at (0, 1) {};
                \node at (0, 0.2) {\vdots};
                \node[neuron] (I4) at (0, -0.7) {};
                \node[layer] at (0, -1.3) {$2^n$ features};
            
                % 2nd Input layer nodes (4 input neurons)
                \node[neuron] (I21) at (1.5, 3) {};
                \node[neuron] (I22) at (1.5, 2) {};
                \node[neuron] (I23) at (1.5, 1) {};
                \node at (1.5, 0.2) {\vdots};
                \node[neuron] (I24) at (1.5, -0.7) {};
                \node[layer] at (1.5, 3.6) {$2^n$ Act.};
            
                % 1st to 2nd Input
                \draw[->] (I1) -- (I21);
                \draw[->] (I1) -- (I22);
                \draw[->] (I1) -- (I23);
                \draw[->] (I1) -- (I24);
                \draw[->] (I2) -- (I21);
                \draw[->] (I2) -- (I22);
                \draw[->] (I2) -- (I23);
                \draw[->] (I2) -- (I24);
                \draw[->] (I3) -- (I21);
                \draw[->] (I3) -- (I22);
                \draw[->] (I3) -- (I23);
                \draw[->] (I3) -- (I24);
                \draw[->] (I4) -- (I21);
                \draw[->] (I4) -- (I22);
                \draw[->] (I4) -- (I23);
                \draw[->] (I4) -- (I24);
                \end{tikzpicture}
            };
            \node[above=5pt of box1] {$2^n$ Input Classical Data};
            
            % Place the quantum circuit next to it
            \node[draw, thick, rectangle, fill=blue!30] (box2) at (7.0, 0) {
                \begin{quantikz}
                    \lstick{} & \push{\ket{\psi}} \gategroup[3,steps=1,style={inner
                    sep=6pt},label style={label position=above,yshift=0.2cm}]{Amplitude Enc.} & \gate{R(\alpha_1, \beta_1, \gamma_1)} & \ctrl{1} & \qw & \qw & \targ{} & \meter{Z} & \lstick{} \\
                    \lstick{} & \push{\ket{\psi}} & \gate{R(\alpha_2, \beta_2, \gamma_2)} & \targ{} & \ctrl{1} & \qw & \qw & \meter{Z} & \lstick{}\\
                    \lstick{} & \push{\ket{\psi}} & \gate{R(\alpha_3, \beta_3, \gamma_3)} & \qw & \targ{} & \qw & \ctrl{-2} & \meter{Z} & \lstick{}
                \end{quantikz}
            };
            \node[above=5pt of box2] {PQC};
        
            \node (out) at (12.7,0) {
                \begin{tikzpicture}[
                    neuron/.style={circle, draw, minimum size=0.8cm},
                    dropout/.style={circle, draw, minimum size=0.8cm, dashed},
                    layer/.style={draw=none,fill=none}
                ]
                    % Output layer nodes (4 input neurons)
                    \node[neuron] (O1) at (0, 0) {};
                \end{tikzpicture}
            };
            \node[above=5pt of out] {Output};
                
            \draw[->] (box1) -- (box2);
            \draw[->] (box2) -- (out);
        \end{tikzpicture}
        }
        % \caption{The QML model with Amplitude Encoding. We encode a set of $N = 2^n$ classical data into the amplitude of the input quantum state denoted as $\ket{\psi}$. After the application of the Strongly Entangling PQC, a measurement is performed. These measurement results are then sent to the classical output layer and post-processed in the classical optimizer.}
        % \label{fig:ampl}
    \end{figure}
\end{document}