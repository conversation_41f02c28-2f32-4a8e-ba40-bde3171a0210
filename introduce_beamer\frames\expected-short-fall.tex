%------------------------------------------------

\begin{frame}[fragile]
  \frametitle{Expected Shortfall 计算演示}
  
  \small
  \begin{columns}  
    
    \begin{column}{0.4\textwidth}
      \begin{block}{ES计算步骤}
        \begin{enumerate}
          \item \textcolor{blue}{\textbf{情景生成}}\\
          生成市场数据情景文件
          
          \item \textcolor{green!70!black}{\textbf{估值计算}}\\
          调用估值引擎计算情景PV
          
          \item \textcolor{orange}{\textbf{损益计算}}\\
          计算损益序列 (情景PV - 基准PV)
          
          \item \textcolor{purple}{\textbf{序列排序}}\\
          对损益序列排序
          
          \item \textcolor{red}{\textbf{ES计算}}\\
          取分位数计算ES
          
          \item \textcolor{gray!70!black}{\textbf{流动性调整}}\\
          考虑流动性期限调整
        \end{enumerate}
      \end{block}
    \end{column}

    \begin{column}{0.58\textwidth}
      \textbf{示例代码}：
        \begin{lstlisting}[language=Python, basicstyle=\tiny, breaklines=true]
def calculate_es(self, 
    pnl_series: pd.Series, 
    liquidity_period: int = 0) -> float:
    """计算单一流动性期限下的ES"""
    
    # 1. 排序损益序列
    sorted_pnl = pnl_series.sort_values()
    
    # 2. 确定分位数p
    p = 1.0 - self.confidence_level \
        if self.is_mirror \
        else self.confidence_level
    
    # 3. 计算VaR分位点
    var_index = int(len(sorted_pnl) * p)
    
    # 4. 计算ES (尾部均值)
    if self.is_mirror:
        es_value = sorted_pnl[:var_index].mean()
    else:
        es_value = sorted_pnl[var_index:].mean()
    
    # 5. 流动性期限调整
    if liquidity_period > 0:
        es_value *= sqrt(liquidity_period)
    
    return abs(es_value)
        \end{lstlisting}
    \end{column}

  \end{columns}
\end{frame}

%------------------------------------------------