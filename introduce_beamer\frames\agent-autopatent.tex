\begin{frame}
    \frametitle{AutoPatent - 多智能体专利自动生成系统}
    
    \begin{columns}[t]
        \begin{column}{0.48\textwidth}
            \begin{block}{🤖 核心架构}
                \begin{itemize}
                    \item \textbf{规划智能体} (Planner)
                    \item \textbf{撰写智能体} (Writer) 
                    \item \textbf{审查智能体} (Examiner)
                \end{itemize}
            \end{block}
            
            \begin{block}{🌳 技术特性}
                \begin{itemize}
                    \item \textbf{PGTree结构}：专利生成树
                    \item \textbf{RRAG机制}：检索增强审查
                    \item \textbf{现代化CLI}：typer + rich
                \end{itemize}
            \end{block}
        \end{column}
        
        \begin{column}{0.48\textwidth}
            \begin{block}{💾 系统功能}
                \begin{itemize}
                    \item 专利数据库存储与检索
                    \item 相似性搜索
                    \item 令牌使用追踪
                    \item API成本监控
                \end{itemize}
            \end{block}
            
            \begin{alertblock}{✨ 核心优势}
                结构化生成 + 智能审查 = 高质量专利文档
            \end{alertblock}
        \end{column}
    \end{columns}
    
\end{frame}