\documentclass{ctexbeamer}
\mode<presentation> {
    \usetheme{Madrid}
    \usecolortheme{rose}
    
    %\setbeamertemplate｛footline｝
    %若要删除所有幻灯片中的页脚，请取消注释此行
    
    %\setbeamertemplate｛footline｝[页码]
    %若要用简单的幻灯片计数替换所有幻灯片中的页脚，请取消注释此行
    
    %\setbeamertemplate｛导航符号｝｛｝
    %要删除所有幻灯片底部的导航符号，请取消注释此行
}
\usepackage{listings}      % 用于插入代码
\usepackage{xcolor}        % 用于语法高亮颜色
% 设置 listings 参数
\lstset{
    basicstyle=\ttfamily\small,
    keywordstyle=\color{blue},
    commentstyle=\color{gray},
    stringstyle=\color{red},
    frame=single,
    breaklines=true,
    numbersep=10pt,     % 行号与代码之间的距离，增大此值
    tabsize=4,          % 每个制表符占 4 个空格
    showtabs=false,     % 不显示 ^^I
    showspaces=false,   % 不显示空格标记
    keepspaces=true,    % 保留原始空格
    linewidth=0.6\textwidth, % 限制宽度为页面宽度的 90%
    breaklines=true,         % 启用自动换行
}
\usepackage[T1]{fontenc}   % 使用 T1 字体编码
\usepackage{graphicx}      % 允许包含图像
\usepackage{tikz}          % TikZ 绘图库
\usepackage{quantikz}      % 量子电路绘制
\usepackage{adjustbox}     % 适用于 resizebox
\usepackage{pgfplots}      % 绘制数学图形（可能用到）
\pgfplotsset{compat=1.18}  % 设置 pgfplots 兼容模式
\usepackage{amsmath}       % 数学符号支持
\usepackage{float}         % 允许控制 figure 位置
\usepackage{tikz-3dplot}   % 可能用于 3D 画图
\usepackage{tikzscale}     % 让 TikZ 图形可缩放
\usepackage{booktabs}      % 允许在表中使用\toprule、\ midrule和\ bottomrule
\usepackage[bookmarks=true]{hyperref}
\usetikzlibrary{positioning, shapes, arrows, calc, decorations.pathreplacing, matrix, fit, backgrounds} % 额外 TikZ 库
\usetikzlibrary{chains, shapes.symbols, shapes.geometric, arrows.meta} % 使用更丰富的箭头样式

%-----------------------------------
%	以下为正文
%-----------------------------------
% 简短标题显示在每张幻灯片的底部，完整标题仅在标题页上
\title[量子技术的影响与应用]{Quantum Technology: Computing and Sensing} 
\author{张晓旭} % Your name
\institute[CCBFT] % 您的机构将出现在每张幻灯片的底部，可能是节省空间的简写
{
    CCB Fintech Co. Ltd. \\ % 你所在的机构
    \medskip
    \textit{<EMAIL>} % Your email address
}
\date{\today} % 日期，可以更改为自定义日期

\begin{document}
    %------------------------------------------------

    \begin{frame}
        \titlepage % 将标题页打印为第一张幻灯片
    \end{frame}

    %------------------------------------------------

    \begin{frame}{目录}
        \begin{itemize}
            \item[1.] 量子计算
            \begin{itemize}
                \item 硬件
                \item 算法应用
                \item 行业应用
                \item 瓶颈
            \end{itemize}
            \item[2.] 量子传感
            \begin{itemize}
                \item 光与原子相互作用
                \item 电气性能
                \item 心磁测量
                \item 脑磁测量
            \end{itemize}
            \item[3.] 磁测量工程应用
            \begin{itemize}
                \item 惯性测量姿态控制
                \item 水平定向钻
                \item 管棚钻孔平行度控制
            \end{itemize}
        \end{itemize}
    \end{frame}
    
    %------------------------------------------------
    %	开始创建PPT正文内容
    %------------------------------------------------
    
    \section{量子计算} % 可以创建章节，以便将您的演讲组织成离散的块，所有章节和小节都会自动打印在目录中，作为演讲的概述
    \subsection{硬件} % 可以在一组具有共同主题的幻灯片之前创建一个小节，以进一步将您的演示分解为块
    \input{frames/quantum-computing-chips.tex}
    \input{frames/superconduct-QC-electronic-structure.tex}
    \input{frames/superconduct-QC-magnetic-shielding.tex}
    \input{frames/superconduct-qubit-electronic-structure.tex}
    \input{frames/superconduct-qubit-physical-model.tex}
    \input{frames/superconduct-qubit-simulation.tex}
    \subsection{算法应用}
    \input{frames/algorithm-NISQ-VQE.tex}
    \input{frames/algorithm-NISQ-QAOA.tex}
    \input{frames/algorithm-sequential-stock-QLSTM.tex}
    \input{frames/algorithm-sequential-stock-transformer.tex}
    \subsection{行业应用}
    \input{frames/application-Finq-QAAS.tex}
    \input{frames/application-Finq-riskManagement.tex}
    \input{frames/application-Finq-Bayesian.tex}
    \input{frames/application-Finq-feature-selection.tex}
    \subsection{瓶颈}
    \input{frames/quantum-computing-theory-danger.tex}
    \input{frames/quantum-computing-business-danger.tex}

    %------------------------------------------------    

    \section{量子传感}
    \subsection{光与原子相互作用}
    \input{frames/quantum-sensing-atomic-interaction.tex}
    \subsection{电气性能}
    \input{frames/quantum-sensing-electrical-performance.tex}
    \input{frames/quantum-sensing-electrical-PSD.tex}
    \input{frames/quantum-sensing-lockin-amplifier.tex}
    \subsection{心磁测量} 
    \input{frames/biomagnetism-TDK-TMR.tex}
    \input{frames/biomagnetism-heart-magnet.tex}
    \subsection{脑磁测量} 
    \input{frames/biomagnetism-brain-magnet.tex}
    \input{frames/biomagnetism-deep-learning-insight.tex}

    %------------------------------------------------

    \section{磁测量工程应用}
    \subsection{惯性测量姿态控制}
    \input{frames/business-antenna-alignment.tex}
    \input{frames/business-GPS-INS.tex}
    \subsection{水平定向钻}
    \input{frames/business-horizontal-directional-drilling.tex}
    \input{frames/business-parallel-holes.tex}
    \subsection{管棚钻孔平行度控制}  
    \input{frames/business-freezing-holes.tex}
    \input{frames/business-freezing-microCoil.tex}
    
    %------------------------------------------------

    \begin{frame}
        \Huge{\centerline{Thank you all！}}
        \centering{{\normalsize If you are not completely confused by quantum mechanics, you do not understand it.}}
    \end{frame}

    %------------------------------------------------

\end{document}