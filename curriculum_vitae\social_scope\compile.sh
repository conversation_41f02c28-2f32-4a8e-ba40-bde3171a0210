#!/bin/bash

echo "正在编译简历..."
echo

echo "第一步：运行 XeLaTeX..."
xelatex cv-llt.tex
if [ $? -ne 0 ]; then
    echo "XeLaTeX 编译失败！"
    exit 1
fi

echo
echo "第二步：运行 Biber 处理参考文献..."
biber cv-llt
if [ $? -ne 0 ]; then
    echo "Biber 处理失败！"
    exit 1
fi

echo
echo "第三步：再次运行 XeLaTeX 生成最终文档..."
xelatex cv-llt.tex
if [ $? -ne 0 ]; then
    echo "第二次 XeLaTeX 编译失败！"
    exit 1
fi

echo
echo "第四步：最后一次运行 XeLaTeX 确保所有引用正确..."
xelatex cv-llt.tex
if [ $? -ne 0 ]; then
    echo "第三次 XeLaTeX 编译失败！"
    exit 1
fi

echo
echo "编译完成！PDF 文件已生成。"
echo
