%------------------------------------------------

\begin{frame}[fragile]
    \frametitle{浏览器智能体 Browser Use}

    \begin{block}{核心功能}
        \begin{itemize}
        \item 利用LLM推理能力实现批量设计文档的信息提取与质量打分。
        \item 利用browser-use智能体把提取信息填入前端表单。
        \item 支持扫描版PDF、错误处理和结果存储。
        \end{itemize}
    \end{block}

    \begin{block}{提示词}
        \begin{lstlisting}[language=Python]
async def fill_web_form(elements, form_url, filename_prefix):
    task = f"""
1. Navigate to {form_url}
2. Fill the form field with id 'title' with '{elements['title']}'
...
12. Click the button with id 'submitButton'
13. Wait for '提交成功！数据已保存。'
"""
    agent = Agent(task=task, llm=ChatOpenAI(...), use_vision=True)
    result = await agent.run()
    form_result = {'elements': elements, 'form_result': result}
    save_results(form_result, filename_prefix)
    return form_result
        \end{lstlisting}
    \end{block}

\end{frame}

%------------------------------------------------