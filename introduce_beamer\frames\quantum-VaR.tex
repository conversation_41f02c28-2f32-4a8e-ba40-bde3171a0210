%------------------------------------------------

\begin{frame}[fragile]
  \frametitle{量子计算风险价值(VaR)分析项目}

  \begin{columns}
    \begin{column}{0.45\textwidth}
      \begin{block}{项目工作流程}
        \textbf{1. 数据预处理}
        \begin{itemize}
          \item 加载股票历史数据
          \item 计算日收益率
          \item 拟合正态/t分布参数
        \end{itemize}

        \textbf{2. 用户交互设置}
        \begin{itemize}
          \item 选择分布类型
          \item 设置资产价值、时间窗口
          \item 配置置信水平参数
        \end{itemize}

        \textbf{3. VaR计算方法}
        \begin{itemize}
          \item 参数法
          \item 蒙特卡洛模拟
          \item 历史模拟
          \item \textcolor{red}{\textbf{量子蒙特卡洛}}
        \end{itemize}
      \end{block}
    \end{column}

    \begin{column}{0.45\textwidth}
      \begin{block}{核心量子算法}
        \textbf{量子振幅估计原理：}
        \begin{enumerate}
          \item 将收益率分布加载到\\量子态振幅
          \item 构建量子比较器实现\\累积分布函数
          \item 使用迭代量子振幅估计\\(IQAE)寻找分位点
          \item 二分搜索优化估计过程
        \end{enumerate}
      \end{block}

      \vspace{0.2cm}
      \begin{alertblock}{量子优势}
        \begin{itemize}
          \item 指数级加速蒙特卡洛采样
          \item 高精度分位数估计
          \item 并行处理多个置信水平
        \end{itemize}
      \end{alertblock}
    \end{column}
  \end{columns}

  \vspace{0.2cm}
  \begin{center}
    \textit{利用量子叠加和振幅估计实现高效风险度量}
  \end{center}
\end{frame}

%------------------------------------------------