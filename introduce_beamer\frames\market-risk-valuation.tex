%------------------------------------------------

\begin{frame}
  \frametitle{市场风险及估值计算系统架构}
  
  \small
  \begin{columns}
    \begin{column}{0.45\textwidth}
      \begin{block}{Black-Derman-Toy利率模型}
        \textbf{BDT模型}
        \begin{itemize}
          \item 构建利率二叉树
          \item 含权债券估值
        \end{itemize}
        
        \textbf{外汇远期模型}
        \begin{itemize}
          \item 外汇远期合约估值
          \item PV拆分与币种转换
        \end{itemize}
      \end{block}
      
      \begin{block}{风险模型}
        \textbf{预期尾部损失模型}
        \begin{itemize}
          \item ES风险指标计算
          \item 多流动性期限支持
          \item 符合巴塞尔III标准
        \end{itemize}
      \end{block}
    \end{column}
    
    \begin{column}{0.45\textwidth}
      \begin{block}{计算引擎}
        \textbf{估值引擎}
        \begin{itemize}
          \item 模型注册表管理
          \item 投资组合估值
        \end{itemize}
        
        \textbf{风险计量引擎}
        \begin{itemize}
          \item 情景估值计算
          \item 风险指标处理
          \item 风险分解分析
        \end{itemize}
      \end{block}
      
      \begin{block}{数据处理}
        \textbf{情景生成器}
        \begin{itemize}
          \item 基于历史数据生成市场情景
          \item 多种变动计算方法
        \end{itemize}
      \end{block}
    \end{column}
  \end{columns}
  
  \vspace{0.2cm}
  \begin{center}
    \textit{构建完整的估值与风险管理计算框架}
  \end{center}
\end{frame}

%------------------------------------------------